import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';

/**
 * Service xử lý logic nghiệp vụ cho Agent System
 */
@Injectable()
export class AdminAgentSystemService {
  private readonly logger = new Logger(AdminAgentSystemService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentSystemRepository: AgentSystemRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly employeeInfoService: EmployeeInfoService,
  ) { }

  /**
   * <PERSON><PERSON>y danh sách agent system với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system với phân trang
   */
  // async findAll(
  //   queryDto: AgentSystemQueryDto,
  // ): Promise<PaginatedResult<AgentSystemListItemDto>> {
  //   const { page, limit, search, status, sortBy, sortDirection } = queryDto;

  //   // Lấy danh sách agent từ repository
  //   const result = await this.agentRepository.findPaginated(
  //     page,
  //     limit,
  //     search,
  //     status,
  //     sortBy,
  //     sortDirection,
  //   );

  //   try {
  //     // Chuyển đổi kết quả sang DTO
  //     const items = await Promise.all(
  //       result.items.map(async (agent) => {
  //         // Lấy vai trò của agent system
  //         const roles = await this.agentRoleRepository.findByAgentSystemId(
  //           agent.id,
  //           this.agentSystemRepository
  //         );

  //         // Lấy vai trò đầu tiên nếu có
  //         const roleDto: SimpleRoleDto | null = roles.length > 0
  //           ? { id: roles[0].id, name: roles[0].name }
  //           : null;

  //         return this.mapToListItemDto(agent, roleDto);
  //       }),
  //     );

  //     return {
  //       items,
  //       meta: {
  //         totalItems: result.total,
  //         itemCount: items.length,
  //         itemsPerPage: limit,
  //         totalPages: Math.ceil(result.total / limit),
  //         currentPage: page,
  //       },
  //     };
  //   } catch (error) {
  //     throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // /**
  //  * Lấy thông tin chi tiết agent system theo ID
  //  * @param id ID của agent system
  //  * @returns Thông tin chi tiết agent system
  //  */
  // async findById(id: string): Promise<AgentSystemDetailDto> {
  //   try {
  //     // Lấy thông tin agent system với tất cả dữ liệu liên quan trong một query tối ưu
  //     const agentSystemDetail = await this.getAgentSystemWithAllDetails(id);

  //     if (!agentSystemDetail) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //     }

  //     // Lấy thông tin vector store
  //     const vectorStore = await this.getVectorStoreInfo(agentSystemDetail.agent);

  //     return await this.mapToDetailDtoOptimized(
  //       agentSystemDetail.agent,
  //       agentSystemDetail.agentSystem,
  //       vectorStore,
  //       agentSystemDetail.role ? {
  //         id: agentSystemDetail.role.id,
  //         name: agentSystemDetail.role.name
  //       } : null,
  //       agentSystemDetail.createdByEmployee,
  //       agentSystemDetail.updatedByEmployee,
  //       agentSystemDetail.deletedByEmployee,
  //     );
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error finding agent system by id: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
  //   }
  // }

  // /**
  //  * Tạo agent system mới
  //  * @param createDto Dữ liệu tạo agent system
  //  * @param employeeId ID của nhân viên tạo
  //  * @returns URL tải lên avatar
  //  */
  // @Transactional()
  // async create(
  //   createDto: CreateAgentSystemDto,
  //   employeeId: number,
  // ): Promise<{
  //   id: string;
  //   avatarUrlUpload?: string;
  // }> {
  //   // Kiểm tra tên agent đã tồn tại chưa
  //   const existingAgent = await this.agentRepository.findByName(createDto.name);

  //   if (existingAgent) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS);
  //   }

  //   // Kiểm tra mã định danh đã tồn tại chưa
  //   const existingAgentByNameCode = await this.agentSystemRepository.findByNameCode(createDto.nameCode);

  //   if (existingAgentByNameCode) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
  //   }

  //   // Validate model_base_id nếu có
  //   if (createDto.modelBaseId) {
  //     await this.validateBaseModel(createDto.modelBaseId);
  //   }

  //   // Validate model_finetuning_id nếu có
  //   if (createDto.modelFinetuningId) {
  //     await this.validateFineTuningModel(createDto.modelFinetuningId);
  //   }

  //   // Kiểm tra vector store có tồn tại không (nếu có)
  //   if (createDto.vectorStoreId) {
  //     await this.validateVectorStore(createDto.vectorStoreId);
  //   }

  //   // Kiểm tra vai trò có tồn tại không (nếu có)
  //   if (createDto.roleId) {
  //     await this.validateRole(createDto.roleId);
  //   }

  //   try {

  //     // Tạo agent mới
  //     const agent = new Agent();
  //     agent.name = createDto.name;
  //     agent.modelConfig = createDto.modelConfig;
  //     agent.instruction = createDto.instruction;
  //     agent.status = createDto.status || 'DRAFT';

  //     // Tạo agent system
  //     const agentSystem = new AgentSystem();
  //     agentSystem.nameCode = createDto.nameCode;
  //     agentSystem.modelBaseId = createDto.modelBaseId || null;
  //     agentSystem.modelFinetuningId = createDto.modelFinetuningId || null;
  //     agentSystem.createdBy = employeeId;
  //     agentSystem.updatedBy = employeeId;

  //     // Gán roleId nếu có
  //     if (createDto.roleId) {
  //       // Kiểm tra vai trò có tồn tại không
  //       await this.validateRole(createDto.roleId);
  //       agentSystem.roleId = createDto.roleId;
  //     } else {
  //       // Nếu không có roleId, sử dụng một giá trị mặc định hoặc ném lỗi
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND, 'Vai trò là bắt buộc cho agent system');
  //     }

  //     // Tạo URL tải lên avatar (nếu có)
  //     let avatarUrlUpload: { url: string; key: string | null } | undefined;
  //     if (createDto.avatarMimeType) {
  //       // Tạo URL tải lên avatar
  //       avatarUrlUpload = await AvatarUrlHelper.generateUploadUrl(
  //         this.s3Service,
  //         employeeId.toString(),
  //         createDto.avatarMimeType
  //       );

  //       agent.avatar = avatarUrlUpload.key;
  //     }

  //     // Lưu agent
  //     const savedAgent = await this.agentRepository.save(agent);

  //     // Gán ID của agent cho agentSystem
  //     agentSystem.id = savedAgent.id;

  //     // Lưu agent system
  //     const agentSystemSaved = await this.agentSystemRepository.save(agentSystem);

  //     const url = avatarUrlUpload ? avatarUrlUpload.url : undefined;

  //     return { id: agentSystemSaved.id, avatarUrlUpload: url };
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error creating agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
  //   }
  // }

  // /**
  //  * Cập nhật thông tin agent system
  //  * @param id ID của agent system
  //  * @param updateDto Dữ liệu cập nhật
  //  * @param employeeId ID của nhân viên cập nhật
  //  * @returns URL tải lên avatar mới (nếu có)
  //  */
  // @Transactional()
  // async update(
  //   id: string,
  //   updateDto: UpdateAgentSystemDto,
  //   employeeId: number,
  // ): Promise<{ avatarUrlUpload?: string }> {
  //   // Kiểm tra agent system có tồn tại không
  //   const agent = await this.agentRepository.findById(id);
  //   if (!agent) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //   }

  //   const agentSystem = await this.agentSystemRepository.findById(id);
  //   if (!agentSystem) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }

  //   // Kiểm tra tên agent đã tồn tại chưa (nếu có cập nhật tên)
  //   if (updateDto.name && updateDto.name !== agent.name) {
  //     const existingAgent = await this.agentRepository.findByName(
  //       updateDto.name,
  //     );
  //     if (existingAgent && existingAgent.id !== id) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS);
  //     }
  //   }

  //   // Kiểm tra mã định danh đã tồn tại chưa (nếu có cập nhật mã định danh)
  //   if (updateDto.nameCode && updateDto.nameCode !== agentSystem.nameCode) {
  //     const existingAgentByNameCode = await this.agentSystemRepository.findByNameCode(
  //       updateDto.nameCode,
  //     );
  //     if (existingAgentByNameCode && existingAgentByNameCode.id !== id) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
  //     }
  //   }

  //   // Validate model_base_id nếu có cập nhật
  //   if (updateDto.modelBaseId !== undefined && updateDto.modelBaseId !== null) {
  //     await this.validateBaseModel(updateDto.modelBaseId);
  //   }

  //   // Validate model_finetuning_id nếu có cập nhật
  //   if (updateDto.modelFinetuningId !== undefined && updateDto.modelFinetuningId !== null) {
  //     await this.validateFineTuningModel(updateDto.modelFinetuningId);
  //   }

  //   // Kiểm tra vector store có tồn tại không (nếu có cập nhật vector store)
  //   if (updateDto.vectorStoreId) {
  //     await this.validateVectorStore(updateDto.vectorStoreId);
  //   }

  //   // Kiểm tra vai trò có tồn tại không (nếu có cập nhật vai trò)
  //   if (updateDto.roleId) {
  //     await this.validateRole(updateDto.roleId);
  //   }

  //   try {
  //     // Cập nhật thông tin agent
  //     if (updateDto.name) agent.name = updateDto.name;
  //     if (updateDto.modelConfig) agent.modelConfig = updateDto.modelConfig;
  //     if (updateDto.instruction !== undefined)
  //       agent.instruction = updateDto.instruction;
  //     if (updateDto.status) agent.status = updateDto.status;

  //     // Cập nhật thông tin agent system
  //     if (updateDto.nameCode) agentSystem.nameCode = updateDto.nameCode;
  //     if (updateDto.modelBaseId !== undefined) agentSystem.modelBaseId = updateDto.modelBaseId;
  //     if (updateDto.modelFinetuningId !== undefined) agentSystem.modelFinetuningId = updateDto.modelFinetuningId;
  //     agentSystem.updatedBy = employeeId;

  //     // Tạo URL tải lên avatar mới (nếu có)
  //     let avatarUrlUpload: { url: string; key: string | null } | undefined;
  //     if (updateDto.avatarMimeType) {
  //       // Tạo URL tải lên avatar
  //       avatarUrlUpload = await AvatarUrlHelper.generateUploadUrl(
  //         this.s3Service,
  //         employeeId.toString(),
  //         updateDto.avatarMimeType,
  //         id,
  //         agent.avatar || undefined,
  //       );

  //       if (avatarUrlUpload.key) {
  //         agent.avatar = avatarUrlUpload.key;
  //       }
  //     }

  //     // Lưu agent
  //     await this.agentRepository.save(agent);

  //     // Lưu agent system
  //     await this.agentSystemRepository.save(agentSystem);

  //     // Gán vai trò cho agent system (nếu có)
  //     if (updateDto.roleId) {
  //       await this.assignRoleToAgentSystem(id, updateDto.roleId);
  //     }

  //     const url = avatarUrlUpload ? avatarUrlUpload.url : undefined;

  //     return { avatarUrlUpload: url };
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error updating agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  //   }
  // }

  // /**
  //  * Cập nhật trạng thái agent system
  //  * @param id ID của agent system
  //  * @param updateStatusDto Dữ liệu cập nhật trạng thái
  //  * @param employeeId ID của nhân viên cập nhật
  //  */
  // @Transactional()
  // async updateStatus(
  //   id: string,
  //   updateStatusDto: UpdateAgentSystemStatusDto,
  //   employeeId: number,
  // ): Promise<void> {
  //   // Kiểm tra agent system có tồn tại không
  //   const agent = await this.agentRepository.findById(id);
  //   if (!agent) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //   }

  //   const agentSystem = await this.agentSystemRepository.findById(id);
  //   if (!agentSystem) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }

  //   try {
  //     // Cập nhật trạng thái agent
  //     agent.status = updateStatusDto.status;

  //     // Lưu agent
  //     await this.agentRepository.save(agent);

  //     // Cập nhật thông tin agent system
  //     agentSystem.updatedBy = employeeId;

  //     // Lưu agent system
  //     await this.agentSystemRepository.save(agentSystem);
  //   } catch (error) {
  //     this.logger.error(
  //       `Error updating agent system status: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(
  //       AGENT_ERROR_CODES.AGENT_SYSTEM_STATUS_UPDATE_FAILED,
  //     );
  //   }
  // }

  // /**
  //  * Xóa agent system (soft delete)
  //  * @param id ID của agent system
  //  * @param employeeId ID của nhân viên xóa
  //  */
  // @Transactional()
  // async remove(id: string, employeeId: number): Promise<void> {
  //   // Kiểm tra agent system có tồn tại không
  //   const agent = await this.agentRepository.findById(id);
  //   if (!agent) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //   }

  //   const agentSystem = await this.agentSystemRepository.findById(id);
  //   if (!agentSystem) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }

  //   try {
  //     // Cập nhật thông tin xóa cho agent system
  //     agentSystem.deletedBy = employeeId;

  //     // Lưu agent system trước khi xóa mềm
  //     await this.agentSystemRepository.save(agentSystem);

  //     // Xóa mềm agent system
  //     await this.agentSystemRepository.softDelete(id);

  //     // Xóa mềm agent
  //     await this.agentRepository.softDelete(id);
  //   } catch (error) {
  //     this.logger.error(
  //       `Error removing agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
  //   }
  // }

  // /**
  //  * Gán vai trò cho agent system
  //  * @param agentId ID của agent system
  //  * @param roleId ID của vai trò
  //  */
  // @Transactional()
  // async assignRoleToAgentSystem(
  //   agentId: string,
  //   roleId: string,
  // ): Promise<void> {
  //   // Kiểm tra agent system có tồn tại không
  //   const agentSystem = await this.agentSystemRepository.findById(agentId);
  //   if (!agentSystem) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }

  //   // Kiểm tra vai trò có tồn tại không
  //   const role = await this.agentRoleRepository.findById(roleId);
  //   if (!role) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
  //   }

  //   try {
  //     // Gán roleId cho agentSystem
  //     agentSystem.roleId = roleId;

  //     // Lưu agent system với roleId mới
  //     await this.agentSystemRepository.save(agentSystem);
  //   } catch (error) {
  //     this.logger.error(
  //       `Error assigning role to agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  //   }
  // }

  // /**
  //  * Lấy danh sách agent system đã xóa với phân trang
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách agent system đã xóa với phân trang
  //  */
  // async getDeletedAgentSystems(
  //   queryDto: AgentSystemQueryDto,
  // ): Promise<PaginatedResult<AgentSystemTrashItemDto>> {
  //   try {
  //     const {
  //       page = 1,
  //       limit = 10,
  //       search,
  //       sortBy = 'createdAt',
  //       sortDirection = 'DESC',
  //     } = queryDto;

  //     const result = await this.agentSystemRepository.findDeletedPaginated(
  //       page,
  //       limit,
  //       search,
  //       sortBy,
  //       sortDirection,
  //     );

  //     // Tạo employee map để lookup nhanh
  //     const employeeMap = new Map();
  //     if (result.deletedEmployees) {
  //       result.deletedEmployees.forEach(emp => {
  //         employeeMap.set(emp.agentSystemId, emp);
  //       });
  //     }

  //     // Lấy thông tin agents tương ứng
  //     const agentIds = result.items.map(item => item.id);
  //     let agents: Agent[] = [];

  //     if (agentIds.length > 0) {
  //       const agentQb = this.agentRepository.createQueryBuilder('agent')
  //         .where('agent.id IN (:...ids)', { ids: agentIds })
  //         .withDeleted(); // Bao gồm cả agents đã xóa

  //       agents = await agentQb.getMany();
  //     }

  //     // Chuyển đổi từ entity sang DTO
  //     const items = await Promise.all(
  //       result.items.map(async (agentSystem) => {
  //         const agent = agents.find(a => a.id === agentSystem.id);

  //         if (!agent) {
  //           this.logger.warn(`Agent not found for agent system ${agentSystem.id}`);
  //           return null;
  //         }

  //         const dto = new AgentSystemTrashItemDto();
  //         dto.id = agent.id;
  //         dto.name = agent.name;
  //         dto.nameCode = agentSystem.nameCode;
  //         dto.avatar = agent.avatar
  //           ? AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar)
  //           : null;
  //         dto.status = agent.status as any;

  //         // Lấy thông tin model
  //         const modelInfo = await this.createModelInfo(agentSystem);
  //         dto.model = modelInfo?.model_id || 'Unknown Model';
  //         dto.model_id = modelInfo?.model_id || null;
  //         dto.type_provider = modelInfo?.typeProvider || null;

  //         // Thông tin người xóa từ employee map
  //         const employeeInfo = employeeMap.get(agentSystem.id);
  //         if (employeeInfo) {
  //           dto.deleted = {
  //             employeeId: employeeInfo.employeeId,
  //             name: employeeInfo.employeeName,
  //             avatar: employeeInfo.employeeAvatar,
  //             date: agent.deletedAt || Date.now(),
  //           };
  //         }

  //         // Lấy thông tin vai trò (nếu có)
  //         if (agentSystem.roleId) {
  //           try {
  //             const role = await this.agentRoleRepository.findById(agentSystem.roleId);
  //             if (role) {
  //               dto.roles = {
  //                 id: role.id,
  //                 name: role.name,
  //               };
  //             }
  //           } catch (error) {
  //             this.logger.warn(`Cannot get role info for agent system ${agentSystem.id}: ${error.message}`);
  //           }
  //         }

  //         return dto;
  //       })
  //     );

  //     // Filter out null items
  //     const validItems = items.filter(item => item !== null) as AgentSystemTrashItemDto[];

  //     return {
  //       items: validItems,
  //       meta: {
  //         totalItems: result.total,
  //         itemCount: validItems.length,
  //         itemsPerPage: limit,
  //         totalPages: Math.ceil(result.total / limit),
  //         currentPage: page,
  //       },
  //     };
  //   } catch (error) {
  //     this.logger.error(
  //       `Error finding deleted agent systems: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }
  // }

  // /**
  //  * Khôi phục agent system đã xóa
  //  * @param id ID của agent system cần khôi phục
  //  * @param employeeId ID của nhân viên thực hiện khôi phục
  //  */
  // @Transactional()
  // async restoreAgentSystem(id: string, employeeId: number): Promise<void> {
  //   try {
  //     // Kiểm tra agent system đã xóa có tồn tại không
  //     const deletedAgentSystem = await this.agentSystemRepository.findOne({
  //       where: { id },
  //       withDeleted: true,
  //     });

  //     if (!deletedAgentSystem || !deletedAgentSystem.deletedBy) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND, 'Agent system đã xóa không tồn tại');
  //     }

  //     // Kiểm tra agent đã xóa có tồn tại không
  //     const deletedAgent = await this.agentRepository.findOne({
  //       where: { id },
  //       withDeleted: true,
  //     });

  //     if (!deletedAgent || !deletedAgent.deletedAt) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND, 'Agent đã xóa không tồn tại');
  //     }

  //     // Khôi phục agent system
  //     const agentSystemRestored = await this.agentSystemRepository.restoreAgentSystem(id, employeeId);
  //     if (!agentSystemRestored) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND, 'Không thể khôi phục agent system');
  //     }

  //     // Khôi phục agent
  //     const agentResult = await this.agentRepository.restore(id);
  //     if (!agentResult.affected || agentResult.affected === 0) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND, 'Không thể khôi phục agent');
  //     }

  //     this.logger.debug(`Đã khôi phục agent system với ID ${id}`);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error restoring agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  //   }
  // }

  // /**
  //  * Xóa vai trò khỏi agent system
  //  * @param agentId ID của agent system
  //  * @param roleId ID của vai trò
  //  */
  // @Transactional()
  // async removeRoleFromAgentSystem(
  //   agentId: string,
  //   roleId: string,
  // ): Promise<void> {
  //   // Kiểm tra agent system có tồn tại không
  //   const agentSystem = await this.agentSystemRepository.findById(agentId);
  //   if (!agentSystem) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }

  //   // Kiểm tra vai trò có tồn tại không
  //   const role = await this.agentRoleRepository.findById(roleId);
  //   if (!role) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
  //   }

  //   try {

  //     // Lưu vai trò
  //     await this.agentRoleRepository.save(role);
  //   } catch (error) {
  //     this.logger.error(
  //       `Error removing role from agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  //   }
  // }

  // /**
  //  * Kiểm tra model có tồn tại không và có trạng thái APPROVED không
  //  * @param modelId ID của model
  //  */
  // private async validateModel(
  //   modelId: string,
  // ): Promise<void> {
  //   try {
  //     // Kiểm tra model có tồn tại không bằng cách gọi service của module Model
  //     const modelResponse = await this.adminModelBaseService.findOne(modelId);
  //     const model = modelResponse.result;

  //     if (!model) {
  //       throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //     }

  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating model: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //   }
  // }

  // /**
  //  * Kiểm tra vector store có tồn tại không
  //  * @param vectorStoreId ID của vector store
  //  */
  // private async validateVectorStore(vectorStoreId: string): Promise<void> {
  //   try {
  //     // Trong thực tế, bạn sẽ gọi service của module Vector Store để kiểm tra
  //     // Ở đây, chúng ta sẽ giả định vector store tồn tại
  //     const vectorStoreExists = true;

  //     if (!vectorStoreExists) {
  //       throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND);
  //     }
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating vector store: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND);
  //   }
  // }

  // /**
  //  * Kiểm tra base model có tồn tại không và có trạng thái APPROVED không
  //  * @param modelBaseId ID của base model
  //  */
  // private async validateBaseModel(modelBaseId: string): Promise<void> {
  //   try {
  //     // Kiểm tra base model có tồn tại không bằng cách gọi service của module Model
  //     const baseModelResponse = await this.adminModelBaseService.findOne(modelBaseId);
  //     const baseModel = baseModelResponse.result;

  //     if (!baseModel) {
  //       throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //     }

  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating base model: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //   }
  // }

  // /**
  //  * Kiểm tra fine-tuning model có tồn tại không và có trạng thái APPROVED không
  //  * @param modelFinetuningId ID của fine-tuning model
  //  */
  // private async validateFineTuningModel(modelFinetuningId: string): Promise<void> {
  //   try {
  //     // TODO: Implement fine-tuning model validation when service is available
  //     // For now, just log the validation attempt
  //     this.logger.debug(`Fine-tuning model validation for ID: ${modelFinetuningId} - skipped (service not implemented)`);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating fine-tuning model: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //   }
  // }

  // /**
  //  * Kiểm tra vai trò có tồn tại không
  //  * @param roleId ID của vai trò
  //  */
  // private async validateRole(roleId: string): Promise<void> {
  //   const role = await this.agentRoleRepository.findById(roleId);
  //   if (!role) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
  //   }
  // }

  // /**
  //  * Lấy thông tin vector store
  //  * @param agent Thông tin agent
  //  * @returns Thông tin vector store hoặc null nếu không có
  //  */
  // private async getVectorStoreInfo(
  //   agent: Agent,
  // ): Promise<VectorStoreDto | null> {
  //   try {
  //     // Kiểm tra xem agent có vectorStoreId không
  //     if (!agent.vectorStoreId) {
  //       return null;
  //     }

  //     // Trong thực tế, bạn sẽ gọi service của module Vector Store để lấy thông tin
  //     // Ở đây, chúng ta sẽ kiểm tra xem vectorStoreId có hợp lệ không
  //     // Nếu vectorStoreId là giá trị mặc định hoặc không hợp lệ, trả về null
  //     if (agent.vectorStoreId === 'vector-store-1' || agent.vectorStoreId === 'default') {
  //       return null;
  //     }

  //     // Trả về thông tin vector store thực sự
  //     return {
  //       vectorStoreId: agent.vectorStoreId,
  //       vectorStoreName: `Vector Store ${agent.vectorStoreId}`,
  //     };
  //   } catch (error) {
  //     this.logger.error(
  //       `Error getting vector store info: ${error.message}`,
  //       error.stack,
  //     );
  //     return null;
  //   }
  // }

  // /**
  //  * Resolve agent model name từ agent base fields
  //  * @param agentId Agent ID để lấy thông tin từ agent base
  //  * @returns Model name
  //  */
  // private async resolveAgentModelName(agentId: string): Promise<string> {
  //   try {
  //     // Lấy thông tin agent base để có model fields
  //     const agentBase = await this.agentBaseRepository.findById(agentId);
  //     if (!agentBase) {
  //       return 'Unknown';
  //     }

  //     if (agentBase.modelBaseId) {
  //       // TODO: Join với base_models table để lấy model name
  //       return `base-model-${agentBase.modelBaseId}`;
  //     }

  //     if (agentBase.modelFinetuningId) {
  //       // TODO: Join với finetuning_models table để lấy model name
  //       return `finetuning-model-${agentBase.modelFinetuningId}`;
  //     }

  //     return 'Unknown';
  //   } catch (error) {
  //     this.logger.error(`Error resolving agent model name: ${error.message}`);
  //     return 'Unknown';
  //   }
  // }

  // /**
  //  * Lấy thông tin nhà cung cấp model
  //  * @param providerId ID của nhà cung cấp (UUID)
  //  * @returns Tên nhà cung cấp
  //  */
  // private async getProviderInfo(providerId: string): Promise<string> {
  //   try {
  //     // Trong thực tế, bạn sẽ gọi service của module Model để lấy thông tin
  //     // Ở đây, chúng ta sẽ tạo một mapping giả định
  //     const providerMap: Record<string, string> = {
  //       '123e4567-e89b-12d3-a456-************': 'Anthropic',
  //       '123e4567-e89b-12d3-a456-************': 'OpenAI',
  //       '123e4567-e89b-12d3-a456-************': 'Google',
  //       '123e4567-e89b-12d3-a456-************': 'Meta AI',
  //     };

  //     return providerMap[providerId] || 'Unknown Provider';
  //   } catch (error) {
  //     this.logger.error(
  //       `Error getting provider info: ${error.message}`,
  //       error.stack,
  //     );
  //     return 'Unknown Provider';
  //   }
  // }

  // /**
  //  * Tạo thông tin model từ model_base_id hoặc model_finetuning_id
  //  * @param agentSystem Entity AgentSystem
  //  * @returns Thông tin model hoặc undefined nếu không có
  //  */
  // private async createModelInfo(agentSystem: AgentSystem): Promise<{ model_id: string; typeProvider: string } | undefined> {
  //   try {
  //     // Nếu có model_base_id, lấy thông tin từ base model
  //     if (agentSystem.modelBaseId) {
  //       try {
  //         const baseModelResponse = await this.adminModelBaseService.findOne(agentSystem.modelBaseId);
  //         const baseModel = baseModelResponse.result;
  //         if (baseModel && baseModel.modelId && baseModel.provider) {
  //           return {
  //             model_id: baseModel.modelId,
  //             typeProvider: baseModel.provider as string,
  //           };
  //         }
  //       } catch (error) {
  //         this.logger.warn(`Không thể lấy thông tin base model ${agentSystem.modelBaseId}: ${error.message}`);
  //       }
  //     }

  //     // TODO: Xử lý model_finetuning_id khi có service
  //     if (agentSystem.modelFinetuningId) {
  //       this.logger.debug(`Finetuning model service chưa được implement: ${agentSystem.modelFinetuningId}`);
  //     }

  //     return undefined;
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi tạo thông tin model: ${error.message}`, error.stack);
  //     return undefined;
  //   }
  // }

  // /**
  //  * Lấy thông tin nhân viên
  //  * @param employeeId ID của nhân viên
  //  * @param date Thời gian tạo/cập nhật/xóa
  //  * @returns Thông tin nhân viên
  //  */
  // private async getEmployeeInfo(employeeId: number, date?: number): Promise<EmployeeInfoSimpleDto> {
  //   const employeeInfo = await this.employeeInfoService.getEmployeeInfo(employeeId);

  //   // Thêm trường date nếu có, sử dụng type assertion để tránh lỗi TypeScript
  //   if (date) {
  //     (employeeInfo as any).date = date;
  //   }

  //   return employeeInfo;
  // }

  // /**
  //  * Chuyển đổi Agent thành AgentSystemListItemDto
  //  * @param agent Entity Agent
  //  * @param roles Danh sách vai trò
  //  * @returns AgentSystemListItemDto
  //  */
  // private async mapToListItemDto(
  //   agent: Agent,
  //   roles: SimpleRoleDto | null,
  // ): Promise<AgentSystemListItemDto> {
  //   try {
  //     // Lấy thông tin agent system
  //     const agentSystem = await this.agentSystemRepository.findById(agent.id);

  //     if (!agentSystem) {
  //       this.logger.warn(`Agent system not found for agent ID: ${agent.id}`);
  //       // Trả về thông tin cơ bản nếu không tìm thấy agent system
  //       // Tạo response DTO
  //       const response: any = {
  //         id: agent.id,
  //         name: agent.name,
  //         nameCode: 'unknown',
  //         avatar: agent.avatar
  //           ? AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar)
  //           : null,
  //         model: await this.resolveAgentModelName(agent.id),
  //         status: agent.status,
  //       };

  //       // Chỉ thêm trường roles nếu có vai trò thực sự
  //       if (roles && roles.id) {
  //         response.roles = roles;
  //       }

  //       return response;
  //     }

  //     // Tạo URL avatar an toàn
  //     let avatarUrl: string | null = null;
  //     if (agent.avatar) {
  //       try {
  //         avatarUrl = AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar);
  //       } catch (avatarError) {
  //         this.logger.error(`Error generating avatar URL: ${avatarError.message}`, avatarError.stack);
  //       }
  //     }

  //     // Lấy thông tin model
  //     const modelInfo = await this.createModelInfo(agentSystem);

  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode,
  //       avatar: avatarUrl,
  //       model: modelInfo?.model_id || 'Unknown Model',
  //       model_id: modelInfo?.model_id || null,
  //       type_provider: modelInfo?.typeProvider || null,
  //       status: agent.status,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   } catch (error) {
  //     this.logger.error(`Error mapping agent to list item DTO: ${error.message}`, error.stack);
  //     // Trả về thông tin cơ bản nếu có lỗi
  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: 'error',
  //       avatar: null,
  //       model: 'Unknown',
  //       status: agent.status,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   }
  // }

  // /**
  //  * Lấy thông tin agent system với tất cả dữ liệu liên quan trong một query tối ưu
  //  * @param id ID của agent system
  //  * @returns Thông tin chi tiết agent system với các dữ liệu liên quan
  //  */
  // private async getAgentSystemWithAllDetails(id: string): Promise<{
  //   agent: Agent;
  //   agentSystem: AgentSystem;
  //   role?: AgentRole;
  //   createdByEmployee?: { id: number; fullName: string; avatar?: string };
  //   updatedByEmployee?: { id: number; fullName: string; avatar?: string };
  //   deletedByEmployee?: { id: number; fullName: string; avatar?: string };
  // } | null> {
  //   try {
  //     // Lấy thông tin agent
  //     const agent = await this.agentRepository.findById(id);
  //     if (!agent) {
  //       return null;
  //     }

  //     // Lấy thông tin agent system
  //     const agentSystem = await this.agentSystemRepository.findById(id);
  //     if (!agentSystem) {
  //       return null;
  //     }

  //     // Lấy thông tin role
  //     let role: AgentRole | undefined;
  //     if (agentSystem.roleId) {
  //       const foundRole = await this.agentRoleRepository.findById(agentSystem.roleId);
  //       role = foundRole || undefined;
  //     }

  //     // Lấy thông tin employees trong một batch để tối ưu
  //     const employeeIds = [
  //       agentSystem.createdBy,
  //       agentSystem.updatedBy,
  //       agentSystem.deletedBy,
  //     ].filter((id): id is number => id !== null && id !== undefined);

  //     const employeeInfoMap = new Map<number, { id: number; fullName: string; avatar?: string }>();

  //     if (employeeIds.length > 0) {
  //       // Batch query employees để giảm số lượng queries
  //       for (const employeeId of employeeIds) {
  //         try {
  //           const employeeInfo = await this.employeeInfoService.getEmployeeInfo(employeeId);
  //           if (employeeInfo) {
  //             employeeInfoMap.set(employeeId, {
  //               id: employeeId,
  //               fullName: employeeInfo.name,
  //               avatar: employeeInfo.avatar || undefined,
  //             });
  //           }
  //         } catch (error) {
  //           this.logger.warn(`Không thể lấy thông tin employee ${employeeId}: ${error.message}`);
  //         }
  //       }
  //     }

  //     return {
  //       agent,
  //       agentSystem,
  //       role,
  //       createdByEmployee: agentSystem.createdBy ? employeeInfoMap.get(agentSystem.createdBy) : undefined,
  //       updatedByEmployee: agentSystem.updatedBy ? employeeInfoMap.get(agentSystem.updatedBy) : undefined,
  //       deletedByEmployee: agentSystem.deletedBy ? employeeInfoMap.get(agentSystem.deletedBy) : undefined,
  //     };
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi lấy chi tiết agent system ${id}: ${error.message}`, error.stack);
  //     return null;
  //   }
  // }

  // /**
  //  * Chuyển đổi Agent và AgentSystem thành AgentSystemDetailDto (optimized version)
  //  * @param agent Entity Agent
  //  * @param agentSystem Entity AgentSystem
  //  * @param vectorStore Thông tin vector store hoặc null
  //  * @param roles Danh sách vai trò
  //  * @param createdByEmployee Thông tin nhân viên tạo
  //  * @param updatedByEmployee Thông tin nhân viên cập nhật
  //  * @param deletedByEmployee Thông tin nhân viên xóa
  //  * @returns AgentSystemDetailDto
  //  */
  // private async mapToDetailDtoOptimized(
  //   agent: Agent,
  //   agentSystem: AgentSystem,
  //   vectorStore: VectorStoreDto | null,
  //   roles: SimpleRoleDto | null,
  //   createdByEmployee?: { id: number; fullName: string; avatar?: string },
  //   updatedByEmployee?: { id: number; fullName: string; avatar?: string },
  //   deletedByEmployee?: { id: number; fullName: string; avatar?: string },
  // ): Promise<AgentSystemDetailDto> {
  //   try {
  //     // Tạo model config response - chỉ bao gồm 4 trường cần thiết
  //     const modelConfig: ModelConfigResponseDto = {
  //       temperature: agent.modelConfig?.temperature,
  //       top_p: agent.modelConfig?.top_p,
  //       top_k: agent.modelConfig?.top_k,
  //       max_tokens: agent.modelConfig?.max_tokens,
  //     };

  //     // Tạo URL avatar an toàn
  //     let avatarUrl: string | null = null;
  //     if (agent.avatar) {
  //       try {
  //         avatarUrl = AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar);
  //       } catch (avatarError) {
  //         this.logger.error(`Error generating avatar URL: ${avatarError.message}`, avatarError.stack);
  //       }
  //     }

  //     // Lấy thông tin model
  //     const modelInfo = await this.createModelInfo(agentSystem);

  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode,
  //       avatar: avatarUrl,
  //       modelConfig,
  //       instruction: agent.instruction,
  //       vector: vectorStore,
  //       status: agent.status,
  //       model_id: modelInfo?.model_id || null,
  //       type_provider: modelInfo?.typeProvider || null,
  //     };

  //     // Thêm thông tin nhân viên từ optimized query
  //     if (createdByEmployee) {
  //       response.created = {
  //         employeeId: createdByEmployee.id,
  //         name: createdByEmployee.fullName,
  //         avatar: createdByEmployee.avatar,
  //         date: agent.createdAt,
  //       };
  //     }

  //     if (updatedByEmployee) {
  //       response.updated = {
  //         employeeId: updatedByEmployee.id,
  //         name: updatedByEmployee.fullName,
  //         avatar: updatedByEmployee.avatar,
  //         date: agent.updatedAt,
  //       };
  //     }

  //     if (deletedByEmployee) {
  //       response.deleted = {
  //         employeeId: deletedByEmployee.id,
  //         name: deletedByEmployee.fullName,
  //         avatar: deletedByEmployee.avatar,
  //         date: agent.deletedAt || undefined,
  //       };
  //     }

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   } catch (error) {
  //     this.logger.error(`Error mapping agent to detail DTO: ${error.message}`, error.stack);
  //     // Trả về thông tin cơ bản nếu có lỗi
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode || 'error',
  //       avatar: null,
  //       modelConfig: {
  //         temperature: undefined,
  //         top_p: undefined,
  //         top_k: undefined,
  //         max_tokens: undefined,
  //       } as ModelConfigResponseDto,
  //       instruction: agent.instruction,
  //       vector: null,
  //       status: agent.status,
  //       created: undefined,
  //       updated: undefined,
  //       deleted: undefined,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   }
  // }

  // /**
  //  * Chuyển đổi Agent và AgentSystem thành AgentSystemDetailDto
  //  * @param agent Entity Agent
  //  * @param agentSystem Entity AgentSystem
  //  * @param vectorStore Thông tin vector store hoặc null
  //  * @param roles Danh sách vai trò
  //  * @returns AgentSystemDetailDto
  //  */
  // private async mapToDetailDto(
  //   agent: Agent,
  //   agentSystem: AgentSystem,
  //   vectorStore: VectorStoreDto | null,
  //   roles: SimpleRoleDto | null,
  // ): Promise<AgentSystemDetailDto> {
  //   // Lấy thông tin nhân viên tạo, cập nhật, xóa
  //   let created: EmployeeInfoDto | undefined, updated: EmployeeInfoDto | undefined, deleted: EmployeeInfoDto | undefined;

  //   try {

  //     // Tạo model config response - chỉ bao gồm 4 trường cần thiết
  //     const modelConfig: ModelConfigResponseDto = {
  //       temperature: agent.modelConfig?.temperature,
  //       top_p: agent.modelConfig?.top_p,
  //       top_k: agent.modelConfig?.top_k,
  //       max_tokens: agent.modelConfig?.max_tokens,
  //     };

  //     // Tạo URL avatar an toàn
  //     let avatarUrl: string | null = null;
  //     if (agent.avatar) {
  //       try {
  //         avatarUrl = AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar);
  //       } catch (avatarError) {
  //         this.logger.error(`Error generating avatar URL: ${avatarError.message}`, avatarError.stack);
  //       }
  //     }

  //     // Lấy thông tin model
  //     const modelInfo = await this.createModelInfo(agentSystem);

  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode,
  //       avatar: avatarUrl,
  //       modelConfig,
  //       instruction: agent.instruction,
  //       vector: vectorStore,
  //       status: agent.status,
  //       model_id: modelInfo?.model_id || null,
  //       type_provider: modelInfo?.typeProvider || null,
  //       created,
  //       updated,
  //       deleted,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   } catch (error) {
  //     this.logger.error(`Error mapping agent to detail DTO: ${error.message}`, error.stack);
  //     // Trả về thông tin cơ bản nếu có lỗi
  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode || 'error',
  //       avatar: null,
  //       modelConfig: {
  //         temperature: undefined,
  //         top_p: undefined,
  //         top_k: undefined,
  //         max_tokens: undefined,
  //       } as ModelConfigResponseDto,
  //       instruction: agent.instruction,
  //       vector: null,
  //       status: agent.status,
  //       created: undefined,
  //       updated: undefined,
  //       deleted: undefined,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   }
  // }
}
