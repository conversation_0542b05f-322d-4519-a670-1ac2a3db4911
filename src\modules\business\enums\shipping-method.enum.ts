/**
 * Enum định nghĩa các phương thức vận chuyển
 * Khớp với service types thực tế của các nhà cung cấp
 */
export enum ShippingMethodEnum {
  /**
   * GHN - Dịch vụ chuẩn (E-commerce)
   */
  GHN_STANDARD = 'Chuẩn',

  /**
   * GHN - Dịch vụ nhanh
   */
  GHN_EXPRESS = 'Nhanh',

  /**
   * GHN - Dịch vụ hỏa tốc
   */
  GHN_URGENT = 'Hỏa tốc',

  /**
   * GHTK - Vận chuyển đường bộ
   */
  GHTK_ROAD = 'Đường bộ',

  /**
   * GHTK - Vận chuyển đường hàng không
   */
  GHTK_FLY = 'Đường hàng không',

  /**
   * Ahamove - Giao hàng trong ngày
   */
  AHAMOVE_SAME_DAY = 'Giao hàng trong ngày',

  /**
   * J&T - Dịch vụ tiêu chuẩn
   */
  JT_STANDARD = 'Tiêu chuẩn'
}

/**
 * <PERSON><PERSON> tả phương thức vận chuyển bằng tiếng Việt
 */
export const SHIPPING_METHOD_DESCRIPTIONS = {
  [ShippingMethodEnum.GHN_STANDARD]: 'GHN Chuẩn - E-commerce (2-3 ngày)',
  [ShippingMethodEnum.GHN_EXPRESS]: 'GHN Nhanh - Express (1-2 ngày)',
  [ShippingMethodEnum.GHN_URGENT]: 'GHN Hỏa tốc - Urgent (trong 24h)',
  [ShippingMethodEnum.GHTK_ROAD]: 'GHTK Đường bộ - Road (3-5 ngày)',
  [ShippingMethodEnum.GHTK_FLY]: 'GHTK Đường hàng không - Fly (1-2 ngày)',
  [ShippingMethodEnum.AHAMOVE_SAME_DAY]: 'Ahamove - Giao trong ngày',
  [ShippingMethodEnum.JT_STANDARD]: 'J&T Tiêu chuẩn - Standard (2-4 ngày)'
};

/**
 * Mapping từ carrier đến shipping method mặc định
 */
export const CARRIER_DEFAULT_SHIPPING_METHOD = {
  'GHN': ShippingMethodEnum.GHN_STANDARD,
  'GHTK': ShippingMethodEnum.GHTK_ROAD,
  'AHAMOVE': ShippingMethodEnum.AHAMOVE_SAME_DAY,
  'JT': ShippingMethodEnum.JT_STANDARD
};

/**
 * Mapping từ carrier đến các shipping methods có sẵn
 */
export const CARRIER_AVAILABLE_SHIPPING_METHODS = {
  'GHN': [
    ShippingMethodEnum.GHN_STANDARD,
    ShippingMethodEnum.GHN_EXPRESS,
    ShippingMethodEnum.GHN_URGENT
  ],
  'GHTK': [
    ShippingMethodEnum.GHTK_ROAD,
    ShippingMethodEnum.GHTK_FLY
  ],
  'AHAMOVE': [
    ShippingMethodEnum.AHAMOVE_SAME_DAY
  ],
  'JT': [
    ShippingMethodEnum.JT_STANDARD
  ]
};

/**
 * Mapping từ shipping method đến service type ID (cho API calls)
 */
export const SHIPPING_METHOD_TO_SERVICE_TYPE = {
  // GHN Service Types
  [ShippingMethodEnum.GHN_STANDARD]: { serviceTypeId: 2, transport: 'road' },
  [ShippingMethodEnum.GHN_EXPRESS]: { serviceTypeId: 2, transport: 'road' },
  [ShippingMethodEnum.GHN_URGENT]: { serviceTypeId: 2, transport: 'fly' },

  // GHTK Transport Types
  [ShippingMethodEnum.GHTK_ROAD]: { transport: 'road' },
  [ShippingMethodEnum.GHTK_FLY]: { transport: 'fly' },

  // Ahamove
  [ShippingMethodEnum.AHAMOVE_SAME_DAY]: { serviceType: 'same_day' },

  // J&T
  [ShippingMethodEnum.JT_STANDARD]: { serviceType: 'standard' }
};
