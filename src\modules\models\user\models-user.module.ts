import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { ServicesModule } from '@shared/services/services.module';

// Import entities
import * as entities from '../entities';

// Import repositories
import * as repositories from '../repositories';

// Import controllers
import {
  UserDataFineTuneController,
  UserKeyLlmController,
} from './controllers';

// Import services
import {
  UserDataFineTuneService,
  UserKeyLlmService,
} from './services';

// Import helpers
import { ApiKeyEncryptionHelper } from '../helpers/api-key-encryption.helper';

/**
 * Module quản lý models cho user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.ModelRegistry,
      entities.SystemKeyLlm,
      entities.FineTuneHistories,
      entities.UserKeyLlm,
      entities.UserDataFineTune,
      entities.AdminDataFineTune,
      // New entities
      entities.UserModels,
      entities.UserModelKeyLlm,
      entities.UserModelFineTune,
      entities.SystemModels,
      entities.SystemModelKeyLlm,
    ]),
    ConfigModule,
    ServicesModule,
  ],
  controllers: [
    UserDataFineTuneController,
    UserKeyLlmController,
  ],
  providers: [
    // Services
    UserDataFineTuneService,
    UserKeyLlmService,

    // Repositories
    repositories.ModelRegistryRepository,
    repositories.SystemKeyLlmRepository,
    repositories.FineTuneHistoriesRepository,
    repositories.UserKeyLlmRepository,
    repositories.UserDataFineTuneRepository,
    repositories.AdminDataFineTuneRepository,

    // Helpers
    ApiKeyEncryptionHelper,
  ],
  exports: [
    UserDataFineTuneService,
    UserKeyLlmService,
  ],
})
export class ModelsUserModule {}
