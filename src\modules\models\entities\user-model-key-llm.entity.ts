import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_model_key_llm trong cơ sở dữ liệu
 * Bảng ánh xạ nhiều-nhiều giữa user_models và user_key_llm
 */
@Entity('user_model_key_llm')
export class UserModelKeyLlm {
  /**
   * ID của user_models
   */
  @PrimaryColumn({ name: 'model_id', type: 'uuid' })
  modelId: string;

  /**
   * ID của user_key_llm
   */
  @PrimaryColumn({ name: 'llm_key_id', type: 'uuid' })
  llmKeyId: string;
}
