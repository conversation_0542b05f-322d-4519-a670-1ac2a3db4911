import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AgentMediaResponseDto } from '../resource/agent-media-response.dto';
import { AgentProductResponseDto } from '../resource/agent-product-response.dto';
import { AgentUrlResponseDto } from '../resource/agent-url-response.dto';

/**
 * DTO cho thông tin cơ bản của agent trong danh sách
 */
export class AgentListItemDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  /**
   * Tên agent
   */
  @ApiProperty({
    description: 'Tên agent',
    example: 'My Assistant',
  })
  name: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://cdn.example.com/avatars/my-assistant.jpg',
  })
  avatar: string | null;

  /**
   * ID loại agent
   */
  @ApiProperty({
    description: 'ID loại agent',
    example: 1,
  })
  typeId: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  typeName: string;

  /**
   * Kinh nghiệm hiện tại của agent
   */
  @ApiProperty({
    description: 'Kinh nghiệm hiện tại của agent',
    example: 150,
  })
  exp: number;

  /**
   * Kinh nghiệm tối đa để lên cấp tiếp theo
   */
  @ApiProperty({
    description: 'Kinh nghiệm tối đa để lên cấp tiếp theo',
    example: 300,
  })
  expMax: number;

  /**
   * Cấp độ hiện tại của agent
   */
  @ApiProperty({
    description: 'Cấp độ hiện tại của agent',
    example: 2,
  })
  level: number;

  /**
   * URL khung rank của agent
   */
  @ApiProperty({
    description: 'URL khung rank của agent',
    example: 'https://cdn.example.com/badges/silver.png',
  })
  badge_url: string;

  /**
   * ID của model mà agent sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của model mà agent sử dụng',
    example: 'gpt-4o',
  })
  model_id?: string;

  /**
    * Loại provider của model
    */
  @ApiPropertyOptional({
    description: 'Loại provider của model',
    example: 'system',
  })
  provider_type?: string;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  active: boolean;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp millis)',
    example: 1672531200000,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật (timestamp millis)',
    example: 1672531200000,
  })
  updatedAt: number;
}

/**
 * DTO cho thông tin về model config (đã loại bỏ model_id và provider_id)
 */
export class ModelConfigDto {
  /**
   * Nhiệt độ (temperature)
   */
  @ApiProperty({
    description: 'Nhiệt độ (temperature)',
    example: 0.7,
  })
  temperature: number;

  /**
   * Top P
   */
  @ApiProperty({
    description: 'Top P',
    example: 0.9,
  })
  top_p: number;

  /**
   * Top K
   */
  @ApiProperty({
    description: 'Top K',
    example: 40,
  })
  top_k: number;

  /**
   * Số token tối đa
   */
  @ApiProperty({
    description: 'Số token tối đa',
    example: 1000,
  })
  max_tokens: number;
}

/**
 * DTO cho thông tin về profile của agent
 */
export class ProfileDto {
  /**
   * Giới tính
   */
  @ApiProperty({
    description: 'Giới tính',
    example: 'MALE',
  })
  gender?: string;

  /**
   * Ngày sinh (timestamp millis)
   */
  @ApiProperty({
    description: 'Ngày sinh (timestamp millis)',
    example: 946684800000,
  })
  dateOfBirth?: number;

  /**
   * Vị trí
   */
  @ApiProperty({
    description: 'Vị trí',
    example: 'Trợ lý AI',
  })
  position?: string;

  /**
   * Học vấn
   */
  @ApiProperty({
    description: 'Học vấn',
    example: 'Đại học',
  })
  education?: string;

  /**
   * Kỹ năng
   */
  @ApiProperty({
    description: 'Kỹ năng',
    example: ['Trả lời câu hỏi', 'Tìm kiếm thông tin'],
    type: [String],
  })
  skills?: string[];

  /**
   * Tính cách
   */
  @ApiProperty({
    description: 'Tính cách',
    example: ['Thân thiện', 'Kiên nhẫn'],
    type: [String],
  })
  personality?: string[];

  /**
   * Ngôn ngữ
   */
  @ApiProperty({
    description: 'Ngôn ngữ',
    example: ['Tiếng Việt', 'Tiếng Anh'],
    type: [String],
  })
  languages?: string[];

  /**
   * Quốc gia
   */
  @ApiProperty({
    description: 'Quốc gia',
    example: 'Việt Nam',
  })
  nations?: string;
}

/**
 * DTO cho thông tin về vector store
 */
export class VectorStoreDto {
  /**
   * ID của vector store
   */
  @ApiProperty({
    description: 'ID của vector store',
    example: 'v1s2t3o4r5e6',
  })
  id: string;

  /**
   * Tên vector store
   */
  @ApiProperty({
    description: 'Tên vector store',
    example: 'My Knowledge Base',
  })
  name: string;
}



/**
 * DTO cho thông tin về tài nguyên của agent
 */
export class ResourcesDto {
  /**
   * Danh sách media
   */
  @ApiProperty({
    description: 'Danh sách media',
    type: [AgentMediaResponseDto],
    required: false,
  })
  media?: AgentMediaResponseDto[];

  /**
   * Danh sách URL
   */
  @ApiProperty({
    description: 'Danh sách URL',
    type: [AgentUrlResponseDto],
    required: false,
  })
  url?: AgentUrlResponseDto[];

  /**
   * Danh sách sản phẩm
   */
  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [AgentProductResponseDto],
    required: false,
  })
  user_product?: AgentProductResponseDto[];
}

/**
 * DTO cho thông tin chi tiết của agent
 */
export class AgentDetailDto extends AgentListItemDto {
  /**
   * Cấu hình model
   */
  @ApiProperty({
    description: 'Cấu hình model',
    type: ModelConfigDto,
  })
  modelConfig: ModelConfigDto;

  /**
   * ID của base model (nếu có)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (nếu có)',
    example: 'base-model-uuid',
  })
  model_base_id?: string;

  /**
   * ID của finetuning model (nếu có)
   */
  @ApiPropertyOptional({
    description: 'ID của finetuning model (nếu có)',
    example: 'finetuning-model-uuid',
  })
  model_finetuning_id?: string;

  /**
   * ID của provider cá nhân (nếu có)
   */
  @ApiPropertyOptional({
    description: 'ID của provider cá nhân (nếu có)',
    example: 'provider-uuid',
  })
  provider_id?: string;

  /**
   * Hướng dẫn (instruction)
   */
  @ApiProperty({
    description: 'Hướng dẫn (instruction)',
    example: 'Bạn là trợ lý cá nhân, hãy giúp người dùng giải đáp các thắc mắc',
  })
  instruction: string;

  /**
   * Thông tin profile
   */
  @ApiProperty({
    description: 'Thông tin profile',
    type: ProfileDto,
  })
  profile: ProfileDto;

  /**
   * Vector store
   */
  @ApiProperty({
    description: 'Vector store',
    type: VectorStoreDto,
    required: false,
  })
  vectorStores?: VectorStoreDto;
}

/**
 * DTO cho thông tin tạo agent thành công
 */
export class CreateAgentResponseDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  /**
   * URL để upload avatar
   */
  @ApiProperty({
    description: 'URL để upload avatar',
    example: 'https://s3.example.com/upload-url?token=xyz',
  })
  avatarUploadUrl: string | null;
}

/**
 * DTO cho thông tin cập nhật agent thành công
 */
export class UpdateAgentResponseDto extends CreateAgentResponseDto { }
