import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho dữ liệu in đơn hàng GHN (token)
 */
export class GHNPrintDataDto {
  @ApiProperty({
    description: 'Token để in đơn hàng GHN',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @Expose()
  token: string;

  @ApiProperty({
    description: 'URL để in đơn hàng GHN',
    example: 'https://online-gateway.ghn.vn/a5/public-api/printA5?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @Expose()
  printUrl: string;

  @ApiProperty({
    description: 'Hướng dẫn sử dụng token',
    example: 'Sử dụng URL này để in đơn hàng khổ A5. Token có hiệu lực trong 24 giờ.'
  })
  @Expose()
  instructions: string;

  @ApiProperty({
    description: 'Format in được chọn',
    example: 'A5'
  })
  @Expose()
  format?: string;
}

/**
 * DTO cho dữ liệu in đơn hàng GHTK (PDF)
 */
export class GHTKPrintDataDto {
  @ApiProperty({
    description: 'Tên file PDF',
    example: 'GHTK_Label_S1.A1.2001297581.pdf'
  })
  @Expose()
  fileName: string;

  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 245760
  })
  @Expose()
  fileSize: number;

  @ApiProperty({
    description: 'URL để tải file PDF',
    example: '/v1/user/orders/20/print/download'
  })
  @Expose()
  downloadUrl: string;

  @ApiProperty({
    description: 'Kích thước giấy',
    example: 'A6',
    enum: ['A5', 'A6']
  })
  @Expose()
  paperSize: string;

  @ApiProperty({
    description: 'Hướng in',
    example: 'portrait',
    enum: ['portrait', 'landscape']
  })
  @Expose()
  orientation: string;
}

/**
 * DTO cho response in đơn hàng
 */
export class PrintOrderResponseDataDto {
  @ApiProperty({
    description: 'ID đơn hàng',
    example: '20'
  })
  @Expose()
  orderId: string;

  @ApiProperty({
    description: 'Đơn vị vận chuyển',
    example: 'GHN',
    enum: ['GHN', 'GHTK']
  })
  @Expose()
  carrier: string;

  @ApiProperty({
    description: 'Mã vận đơn',
    example: 'LBK6X3'
  })
  @Expose()
  trackingNumber: string;

  @ApiProperty({
    description: 'Loại dữ liệu in',
    example: 'token',
    enum: ['token', 'pdf']
  })
  @Expose()
  printType: 'token' | 'pdf';

  @ApiProperty({
    description: 'Dữ liệu in đơn hàng',
    oneOf: [
      { $ref: '#/components/schemas/GHNPrintDataDto' },
      { $ref: '#/components/schemas/GHTKPrintDataDto' }
    ],
    examples: {
      'GHN Token': {
        summary: 'Dữ liệu in cho GHN',
        value: {
          token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          printUrl: 'https://online-gateway.ghn.vn/a5/public-api/printA5?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          instructions: 'Sử dụng URL này để in đơn hàng khổ A5. Token có hiệu lực trong 24 giờ.'
        }
      },
      'GHTK PDF': {
        summary: 'Dữ liệu in cho GHTK',
        value: {
          fileName: 'GHTK_Label_S1.A1.2001297581.pdf',
          fileSize: 245760,
          downloadUrl: '/v1/user/orders/20/print/download',
          paperSize: 'A6',
          orientation: 'portrait'
        }
      }
    }
  })
  @Expose()
  printData: GHNPrintDataDto | GHTKPrintDataDto;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1749016943203
  })
  @Expose()
  createdAt: number;
}

/**
 * DTO cho API response in đơn hàng
 */
export class PrintOrderResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true
  })
  @Expose()
  success: boolean;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Tạo token in đơn hàng thành công'
  })
  @Expose()
  message: string;

  @ApiProperty({
    description: 'Dữ liệu in đơn hàng',
    type: PrintOrderResponseDataDto,
    nullable: true
  })
  @Expose()
  data: PrintOrderResponseDataDto | null;
}
