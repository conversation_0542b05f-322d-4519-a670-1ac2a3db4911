import { Injectable, Logger } from '@nestjs/common';
import { ProductRepository } from '@modules/marketplace/repositories';
import {
  MediaHelper,
  ProductHelper,
  ValidationHelper,
} from '@modules/marketplace/helpers';
import { S3Service } from '@shared/services/s3.service';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { AgentRepository } from '@modules/agent/repositories';
import { UserDataFineTuneRepository, AdminDataFineTuneRepository } from '@modules/models/repositories';
import {
  AddImageOperationDto,
  CreateProductAdminDto,
  DeleteImageOperationDto,
  DeleteMultipleProductsDto,
  ImageOperationType,
  PresignedUrlImageDto,
  PresignedUrlsDto,
  ProductDetailResponseDto,
  ProductResponseDto,
  QueryProductDto,
  UpdateMultipleProductsStatusDto,
  UpdateProductDto,
} from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Product } from '@modules/marketplace/entities';
import { ProductStatus } from '@modules/marketplace/enums';
import { Transactional } from 'typeorm-transactional';
import {
  CategoryFolderEnum,
  FileSizeEnum,
  FileTypeEnum,
  generateS3Key,
  MediaType,
  TimeIntervalEnum,
} from '@shared/utils';

/**
 * Service xử lý logic liên quan đến sản phẩm cho admin
 */
@Injectable()
export class ProductAdminService {
  private readonly logger = new Logger(ProductAdminService.name);

  constructor(
    private readonly productRepository: ProductRepository,
    private readonly productHelper: ProductHelper,
    private readonly validationHelper: ValidationHelper,
    private readonly mediaHelper: MediaHelper,
    private readonly s3Service: S3Service,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly agentRepository: AgentRepository,
    private readonly userDataFineTuneRepository: UserDataFineTuneRepository,
    private readonly adminDataFineTuneRepository: AdminDataFineTuneRepository,
  ) {}

  /**
   * Lấy danh sách sản phẩm với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO truy vấn chứa các tham số tìm kiếm, lọc và phân trang
   * @param currentUserId ID người dùng hiện tại (tùy chọn, cho admin thường không cần)
   * @returns Danh sách sản phẩm phân trang
   */
  async getProducts(
    employeeId: number,
    queryDto: QueryProductDto,
    currentUserId?: number,
  ): Promise<PaginatedResult<ProductResponseDto>> {
    this.logger.log(
      `Employee ${employeeId} is fetching products with query: ${JSON.stringify(queryDto)}`,
    );

    // Nếu có userId, kiểm tra xem có sản phẩm nào thuộc về người dùng này không
    if (queryDto.userId) {
      this.logger.debug(`Checking if user ${queryDto.userId} has any products`);

      // Lấy tất cả sản phẩm để kiểm tra
      const allProducts = await this.productRepository.find();
      this.logger.debug(`Total products in database: ${allProducts.length}`);

      // Kiểm tra từng sản phẩm
      for (const product of allProducts) {
        this.logger.debug(
          `Product ID: ${product.id}, user_id: ${product.userId}, employee_id: ${product.employeeId}`,
        );

        // Kiểm tra xem sản phẩm có thuộc về người dùng cần tìm không
        if (product.userId === queryDto.userId) {
          this.logger.debug(
            `Found product ${product.id} belonging to user ${queryDto.userId}`,
          );
        }
      }

      // Thử truy vấn trực tiếp vào cơ sở dữ liệu để kiểm tra tất cả sản phẩm
      const allRawProducts = await this.productRepository.query(
        `SELECT id, name, user_id, employee_id, status FROM products LIMIT 100`,
      );
      this.logger.debug(
        `Raw query found ${allRawProducts.length} total products`,
      );
      if (allRawProducts.length > 0) {
        this.logger.debug(`All products: ${JSON.stringify(allRawProducts)}`);
      }

      // Thử truy vấn trực tiếp vào cơ sở dữ liệu để kiểm tra sản phẩm của user_id 1
      const rawProducts = await this.productRepository.query(
        `SELECT * FROM products WHERE user_id = $1`,
        [queryDto.userId],
      );
      this.logger.debug(
        `Raw query found ${rawProducts.length} products for user ${queryDto.userId}`,
      );
      if (rawProducts.length > 0) {
        this.logger.debug(
          `First raw product: ${JSON.stringify(rawProducts[0])}`,
        );
      }

      // Thử truy vấn trực tiếp vào cơ sở dữ liệu để kiểm tra sản phẩm của user_id 1 không lọc theo status
      const rawProductsNoStatusFilter = await this.productRepository.query(
        `SELECT * FROM products WHERE user_id = $1`,
        [queryDto.userId],
      );
      this.logger.debug(
        `Raw query (no status filter) found ${rawProductsNoStatusFilter.length} products for user ${queryDto.userId}`,
      );
      if (rawProductsNoStatusFilter.length > 0) {
        this.logger.debug(
          `First raw product (no status filter): ${JSON.stringify(rawProductsNoStatusFilter[0])}`,
        );
      }
    }

    try {
      // Lấy danh sách sản phẩm từ repository - truyền currentUserId để tính isPurchased
      const productsResult = await this.productRepository.findAdminProducts(
        queryDto,
        currentUserId,
      );

      // Nếu không có sản phẩm nào, trả về kết quả trống
      if (productsResult.items.length === 0) {
        return {
          items: [],
          meta: productsResult.meta,
        };
      }

      this.logger.log(`Retrieved ${productsResult.items.length} products`);

      // Chuyển đổi sang DTO
      const productDtos = productsResult.items.map((product) => {
        try {
          return this.productHelper.mapToAdminProductResponseDto(product);
        } catch (dtoError) {
          this.logger.error(
            `Error mapping product ${product.id} to DTO: ${dtoError.message}`,
            dtoError.stack,
          );
          // Ném lỗi để xử lý ở catch block bên ngoài
          throw new AppException(
            MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
            `Lỗi khi chuyển đổi sản phẩm: ${dtoError.message}`,
          );
        }
      });

      // Trả về kết quả phân trang
      return {
        items: productDtos,
        meta: productsResult.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting products: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
        `Không thể lấy danh sách sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết sản phẩm theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần lấy thông tin
   * @param currentUserId ID người dùng hiện tại (tùy chọn, cho admin thường không cần)
   * @returns Thông tin chi tiết sản phẩm
   */
  async getProductById(
    employeeId: number,
    productId: number,
    currentUserId?: number,
  ): Promise<ProductDetailResponseDto> {
    this.logger.log(
      `Employee ${employeeId} is fetching product with ID: ${productId}`,
    );

    try {
      // Lấy thông tin sản phẩm từ repository - truyền currentUserId để tính isPurchased
      const product = await this.productRepository.findByIdForAdmin(
        productId,
        currentUserId,
      );

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Sản phẩm với ID ${productId} không tồn tại`,
        );
      }

      // Kiểm tra sản phẩm không bị xóa
      if (product.status === ProductStatus.DELETED) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_DELETED,
          'Sản phẩm đã bị xóa, không còn tồn tại',
        );
      }

      // Chuyển đổi sang DTO
      return this.productHelper.mapToAdminProductDetailResponseDto(product);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting product by ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
        `Không thể lấy thông tin chi tiết sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo sản phẩm mới với trạng thái mặc định là DRAFT
   * Tạo sản phẩm mới và các URL ký sẵn để upload tài liệu (hình ảnh, hướng dẫn sử dụng, chi tiết)
   * Sản phẩm được tạo với trạng thái DRAFT và chỉ hiển thị cho admin
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param createProductDto Dữ liệu tạo sản phẩm mới
   * @returns Sản phẩm đã tạo cùng với các URL để upload tài liệu
   */
  @Transactional()
  async createProduct(
    employeeId: number,
    createProductDto: CreateProductAdminDto,
  ): Promise<{
    product: ProductDetailResponseDto;
    uploadUrls: {
      productId: string;
      imagesUploadUrls: Array<{ url: string; key: string; index: number }>;
      userManualUploadUrl?: string;
      detailUploadUrl?: string;
    };
  }> {
    // Kiểm tra giá sản phẩm
    this.validationHelper.validateProductPrice(
      createProductDto.listedPrice,
      createProductDto.discountedPrice,
    );

    try {
      const now = Date.now();

      // Kiểm tra trạng thái APPROVED của tài nguyên gốc trước khi tạo sản phẩm
      await this.validationHelper.validateSourceResourceStatus(
        createProductDto.sourceId || null,
        createProductDto.category,
        {
          knowledgeFileRepository: this.knowledgeFileRepository,
          agentRepository: this.agentRepository,
          userDataFineTuneRepository: this.userDataFineTuneRepository,
          adminDataFineTuneRepository: this.adminDataFineTuneRepository,
        }
      );

      // Tạo sản phẩm mới
      const product = new Product();
      product.name = createProductDto.name;
      product.description = createProductDto.description || '';
      product.listedPrice = createProductDto.listedPrice;
      product.discountedPrice = createProductDto.discountedPrice;
      product.category = createProductDto.category;
      product.sourceId = createProductDto.sourceId || null;
      product.status = ProductStatus.DRAFT;
      product.employeeId = employeeId;
      product.createdAt = now;
      product.updatedAt = now;
      product.images = []; // Khởi tạo mảng rỗng, sẽ được cập nhật sau khi upload

      // Tạo các URL upload cho hình ảnh và lưu keys vào database
      const imagesUploadUrls: Array<{
        url: string;
        key: string;
        index: number;
      }> = [];
      const imageEntries: Array<{ key: string; position: number }> = [];

      if (
        createProductDto.imagesMediaTypes &&
        createProductDto.imagesMediaTypes.length > 0
      ) {
        for (let i = 0; i < createProductDto.imagesMediaTypes.length; i++) {
          try {
            const mediaType = createProductDto.imagesMediaTypes[i];
            const imageUploadUrl = await this.createImageUploadUrl(
              `product-image-${i}-${now}`,
              mediaType,
            );

            // Lưu key và position vào mảng để cập nhật database
            imageEntries.push({
              key: imageUploadUrl.key,
              position: i,
            });

            imagesUploadUrls.push({
              url: imageUploadUrl.url,
              key: imageUploadUrl.key,
              index: i,
            });
          } catch (error) {
            this.logger.error(
              `Failed to create image upload URL at index ${i}: ${error.message}`,
              error.stack,
            );
          }
        }
      }

      // Cập nhật mảng images
      product.images = imageEntries;

      // Xử lý hướng dẫn sử dụng
      let userManualKey: string | undefined = undefined;
      let userManualUrl: string | undefined = undefined;

      if (createProductDto.userManualMediaType) {
        try {
          // Xác định FileTypeEnum từ MIME type
          let fileType: FileTypeEnum;
          // Mặc định sử dụng PDF
          fileType = FileTypeEnum.HTML;

          const userManualUploadUrl = await this.createDocumentUploadUrl(
            `product-manual-${now}`,
            fileType,
          );

          userManualKey = userManualUploadUrl.key;
          userManualUrl = userManualUploadUrl.url;

          // Lưu key vào product
          product.userManual = userManualKey;
        } catch (error) {
          this.logger.error(
            `Failed to create user manual upload URL: ${error.message}`,
            error.stack,
          );
        }
      }

      // Xử lý chi tiết sản phẩm
      let detailKey: string | undefined = undefined;
      let detailUrl: string | undefined = undefined;

      if (createProductDto.detailMediaType) {
        try {
          // Xác định FileTypeEnum từ MIME type
          let fileType: FileTypeEnum;

          fileType = FileTypeEnum.HTML;

          const detailUploadUrl = await this.createDocumentUploadUrl(
            `product-detail-${now}`,
            fileType,
          );

          detailKey = detailUploadUrl.key;
          detailUrl = detailUploadUrl.url;

          // Lưu key vào product
          product.detail = detailKey;
        } catch (error) {
          this.logger.error(
            `Failed to create detail upload URL: ${error.message}`,
            error.stack,
          );
        }
      }

      // Lưu sản phẩm vào database chỉ một lần duy nhất
      const savedProduct = await this.productRepository.save(product);

      // Chuyển đổi sang DTO
      const productDto =
        this.productHelper.mapToAdminProductDetailResponseDto(savedProduct);

      // Tạo response
      return {
        product: productDto,
        uploadUrls: {
          productId: savedProduct.id.toString(),
          imagesUploadUrls,
          ...(userManualUrl ? { userManualUploadUrl: userManualUrl } : {}),
          ...(detailUrl ? { detailUploadUrl: detailUrl } : {}),
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to create product: ${error.message}`,
        error.stack,
      );

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        `Không thể tạo sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo URL ký sẵn để upload tài liệu
   * @param fileName Tên file
   * @param fileType Loại file
   * @returns Thông tin URL ký sẵn và key
   * @throws Error nếu có lỗi khi tạo URL
   */
  private async createDocumentUploadUrl(
    fileName: string,
    fileType: FileTypeEnum,
  ): Promise<{ key: string; url: string }> {
    try {
      // Tạo S3 key cho tài liệu
      const key = generateS3Key({
        baseFolder: 'marketplace',
        categoryFolder: CategoryFolderEnum.DOCUMENT,
        fileName,
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const url = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        fileType,
        FileSizeEnum.TEN_MB,
      );

      return { key, url };
    } catch (error) {
      this.logger.error(
        `Failed to create document upload URL for ${fileName}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Không thể tạo URL upload cho tài liệu: ${error.message}`,
      );
    }
  }

  /**
   * Tạo URL ký sẵn để upload hình ảnh
   * @param fileName Tên file
   * @param mediaType Loại media (MIME type)
   * @returns Thông tin URL ký sẵn và key
   * @throws Error nếu có lỗi khi tạo URL
   */
  private async createImageUploadUrl(
    fileName: string,
    mediaType: string,
  ): Promise<{ key: string; url: string }> {
    try {
      // Tạo S3 key cho hình ảnh
      const key = generateS3Key({
        baseFolder: 'marketplace',
        categoryFolder: CategoryFolderEnum.IMAGE,
        fileName,
        useTimeFolder: true,
      });

      // Xác định MediaType từ string sử dụng helper
      const mimeType: MediaType =
        this.mediaHelper.getImageTypeFromMimeString(mediaType);

      // Tạo presigned URL
      const url = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mimeType,
        FileSizeEnum.FIVE_MB,
      );

      return { key, url };
    } catch (error) {
      this.logger.error(
        `Failed to create image upload URL for ${fileName}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Không thể tạo URL upload cho hình ảnh: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin sản phẩm
   * @param employeeId ID của nhân viên hiện tại
   * @param productId ID của sản phẩm cần cập nhật
   * @param updateProductDto Dữ liệu cập nhật sản phẩm
   * @returns Sản phẩm đã cập nhật và các URL để upload tài liệu mới
   */
  @Transactional()
  async updateProduct(
    employeeId: number,
    productId: number,
    updateProductDto: UpdateProductDto,
  ): Promise<{ product: ProductDetailResponseDto } & PresignedUrlsDto> {
    // Tìm sản phẩm theo ID
    const product = await this.productRepository.findById(productId);

    // Kiểm tra sản phẩm tồn tại và thuộc về nhân viên
    this.validationHelper.validateProductEmployeeOwnership(product, employeeId);

    // Sau khi validate, chúng ta chắc chắn product không phải là null
    // TypeScript không biết điều này, nên chúng ta cần khẳng định lại
    if (!product) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
        'Sản phẩm không tồn tại',
      );
    }

    // Kiểm tra trạng thái (đã cập nhật để hỗ trợ cả DRAFT và APPROVED cho sản phẩm của admin)
    this.validationHelper.validateProductIsDraft(product);

    // Kiểm tra giá
    this.validationHelper.validateProductPrice(
      updateProductDto.productInfo.listedPrice,
      updateProductDto.productInfo.discountedPrice,
    );

    try {
      const now = Date.now();

      // Cập nhật thông tin cơ bản
      product.name = updateProductDto.productInfo.name;
      product.listedPrice = updateProductDto.productInfo.listedPrice;
      product.discountedPrice = updateProductDto.productInfo.discountedPrice;

      if (updateProductDto.productInfo.description) {
        product.description = updateProductDto.productInfo.description;
      }

      product.updatedAt = now;

      // Xử lý thao tác với ảnh
      const imagesUploadUrls: Array<PresignedUrlImageDto> = [];

      if (updateProductDto.images && updateProductDto.images.length > 0) {
        // Xử lý các thao tác DELETE trước
        const deleteOperations = updateProductDto.images.filter(
          (img) => img.operation === ImageOperationType.DELETE,
        ) as DeleteImageOperationDto[];
        for (const deleteOp of deleteOperations) {
          // Xóa theo key (bắt buộc cho thao tác DELETE)
          const deleteKey = deleteOp.key;

          this.logger.debug(`Deleting image with key: ${deleteKey}`);
          // Lọc ra các ảnh không bị xóa theo key
          product.images = product.images.filter(
            (img) => img.key !== deleteKey,
          );
        }

        // Xử lý các thao tác ADD
        const addOperations = updateProductDto.images.filter(
          (img) => img.operation === ImageOperationType.ADD,
        ) as AddImageOperationDto[];

        for (const addOp of addOperations) {
          try {
            // Xác định MIME type
            const mimeType = addOp.mimeType;

            // Tìm vị trí lớn nhất hiện tại và tăng lên 1 để có vị trí mới
            const maxPosition =
              product.images.length > 0
                ? Math.max(...product.images.map((img) => img.position))
                : -1;
            const newPosition = maxPosition + 1;
            this.logger.debug(
              `Calculated new position ${newPosition} for new image`,
            );

            const fileName = `product-image-${newPosition}-${now}`;

            // Tạo S3 key cho hình ảnh sản phẩm
            const key = generateS3Key({
              baseFolder: 'marketplace',
              categoryFolder: CategoryFolderEnum.IMAGE,
              fileName,
              useTimeFolder: true,
            });

            // Xác định MediaType từ string sử dụng helper
            const mediaTypeEnum: MediaType =
              this.mediaHelper.getImageTypeFromMimeString(mimeType);

            const url = await this.s3Service.createPresignedWithID(
              key,
              TimeIntervalEnum.FIFTEEN_MINUTES,
              mediaTypeEnum,
              FileSizeEnum.FIVE_MB,
            );

            // Thêm vào danh sách ảnh của sản phẩm với vị trí mới
            product.images.push({
              key,
              position: newPosition,
            });

            // Thêm vào danh sách URL upload
            imagesUploadUrls.push({
              index: newPosition,
              uploadUrl: url,
            });

            this.logger.debug(
              `Created presigned URL for image upload: ${key} with MIME type ${mimeType}`,
            );
          } catch (error) {
            this.logger.error(
              `Failed to create image upload URL: ${error.message}`,
              error.stack,
            );
          }
        }

        // Sắp xếp lại mảng images theo position
        product.images.sort((a, b) => a.position - b.position);
      }

      // Xử lý chi tiết sản phẩm
      let presignedUrlDetail: string | null = null;
      if (updateProductDto.detailEdited) {
        try {
          const fileName = `product-detail-${now}`;
          const detailUploadUrl = await this.createDocumentUploadUrl(
            fileName,
            FileTypeEnum.HTML,
          );

          // Cập nhật key trong database
          product.detail = detailUploadUrl.key;
          presignedUrlDetail = detailUploadUrl.url;
        } catch (error) {
          this.logger.error(
            `Failed to create detail upload URL: ${error.message}`,
            error.stack,
          );
        }
      }

      // Xử lý cập nhật hướng dẫn sử dụng
      let presignedUrlUserManual: string | null = null;
      if (updateProductDto.userManual) {
        try {
          const fileName = `product-manual-${now}`;
          const userManualUploadUrl = await this.createDocumentUploadUrl(
            fileName,
            FileTypeEnum.PDF,
          );

          // Cập nhật key trong database
          product.userManual = userManualUploadUrl.key;
          presignedUrlUserManual = userManualUploadUrl.url;
        } catch (error) {
          this.logger.error(
            `Failed to create user manual upload URL: ${error.message}`,
            error.stack,
          );
        }
      }

      // Cập nhật sản phẩm trong database bằng updateProduct method
      const savedProduct = await this.productRepository.updateProduct(productId, {
        name: product.name,
        description: product.description,
        listedPrice: product.listedPrice,
        discountedPrice: product.discountedPrice,
        images: product.images,
        userManual: product.userManual,
        detail: product.detail,
        updatedAt: product.updatedAt,
      });

      // Chuyển đổi sang DTO
      let productDto =
        this.productHelper.mapToAdminProductDetailResponseDto(savedProduct);

      // Nếu có yêu cầu đăng bán ngay sau khi cập nhật
      let publishError: string | undefined = undefined;
      if (updateProductDto.publishAfterUpdate) {
        this.logger.log(
          `Đăng bán sản phẩm ID ${productId} ngay sau khi cập nhật`,
        );

        try {
          // Kiểm tra sản phẩm có đủ điều kiện để đăng bán không
          this.validationHelper.validateProductForPublish(
            savedProduct,
            employeeId,
          );

          // Cập nhật trạng thái sản phẩm và xử lý các tác vụ liên quan
          const publishedProduct =
            await this.productRepository.updateProductStatusWithRelatedTasks(
              productId,
              ProductStatus.APPROVED,
            );

          // Cập nhật DTO với thông tin mới nhất
          productDto =
            this.productHelper.mapToAdminProductDetailResponseDto(
              publishedProduct,
            );
        } catch (publishError) {
          this.logger.error(
            `Không thể đăng bán sản phẩm ID ${productId}: ${publishError.message}`,
            publishError.stack,
          );

          // Nếu có lỗi khi đăng bán, vẫn trả về sản phẩm đã cập nhật nhưng thêm thông báo lỗi
          publishError =
            publishError instanceof AppException
              ? publishError.message
              : 'Không thể đăng bán sản phẩm sau khi cập nhật';
        }
      }

      return {
        product: productDto,
        presignedUrlImage: imagesUploadUrls,
        presignedUrlDetail,
        presignedUrlUserManual,
        ...(publishError ? { publishError } : {}),
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating product: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        'Lỗi khi cập nhật sản phẩm',
      );
    }
  }

  /**
   * Đăng bán sản phẩm
   * @param employeeId ID của nhân viên
   * @param productId ID của sản phẩm
   * @returns Sản phẩm đã đăng bán
   */
  @Transactional()
  async publishProduct(
    employeeId: number,
    productId: number,
  ): Promise<ProductDetailResponseDto> {
    // Tìm sản phẩm theo ID (không cần currentUserId cho admin)
    const product = await this.productRepository.findById(productId);

    // Kiểm tra sản phẩm có đủ điều kiện để đăng bán không
    // Lưu ý: validateProductForPublish chỉ cho phép đăng bán sản phẩm ở trạng thái DRAFT
    // và chỉ cho phép admin đăng bán sản phẩm của chính mình
    this.validationHelper.validateProductForPublish(product, employeeId);

    try {
      // Cập nhật trạng thái sản phẩm và xử lý các tác vụ liên quan
      const updatedProduct =
        await this.productRepository.updateProductStatusWithRelatedTasks(
          productId,
          ProductStatus.APPROVED,
        );

      // Chuyển đổi sang DTO
      return this.productHelper.mapToAdminProductDetailResponseDto(
        updatedProduct,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error publishing product: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED,
        `Lỗi khi đăng bán sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều sản phẩm (chuyển trạng thái sang DELETED)
   * @param employeeId ID của nhân viên
   * @param deleteMultipleProductsDto DTO chứa danh sách ID sản phẩm cần xóa
   * @returns Danh sách ID sản phẩm đã xóa thành công và danh sách ID sản phẩm thất bại
   */
  @Transactional()
  async deleteMultipleProducts(
    employeeId: number,
    deleteMultipleProductsDto: DeleteMultipleProductsDto,
  ): Promise<{
    successIds: number[];
    failedIds: { id: number; reason: string }[];
  }> {
    const { productIds } = deleteMultipleProductsDto;
    const successIds: number[] = [];
    const failedIds: { id: number; reason: string }[] = [];

    this.logger.log(
      `Employee ${employeeId} đang xóa ${productIds.length} sản phẩm: ${productIds.join(', ')}`,
    );

    // Xử lý từng sản phẩm một cách tuần tự để đảm bảo validation đúng
    for (const productId of productIds) {
      try {
        // Tìm sản phẩm theo ID
        const product = await this.productRepository.findById(productId);

        // Kiểm tra sản phẩm tồn tại
        if (!product) {
          failedIds.push({
            id: productId,
            reason: `Sản phẩm với ID ${productId} không tồn tại`,
          });
          continue;
        }

        // Kiểm tra quyền sở hữu
        try {
          this.validationHelper.validateProductEmployeeOwnership(product, employeeId);
        } catch (error) {
          failedIds.push({
            id: productId,
            reason: error instanceof AppException ? error.message : 'Không có quyền xóa sản phẩm này',
          });
          continue;
        }

        // Kiểm tra quy tắc xóa sản phẩm
        try {
          this.validationHelper.validateProductDeletionRules(product, employeeId);
        } catch (error) {
          failedIds.push({
            id: productId,
            reason: error instanceof AppException ? error.message : 'Không thể xóa sản phẩm này',
          });
          continue;
        }

        // Xóa sản phẩm (chuyển trạng thái sang DELETED)
        const success = await this.productRepository.deleteProductById(productId);

        if (!success) {
          failedIds.push({
            id: productId,
            reason: 'Không thể xóa sản phẩm',
          });
          continue;
        }

        successIds.push(productId);
        this.logger.log(
          `Đã xóa sản phẩm ID ${productId} (chuyển sang trạng thái DELETED)`,
        );
      } catch (error) {
        this.logger.error(
          `Error deleting product ${productId}: ${error.message}`,
          error.stack,
        );
        failedIds.push({
          id: productId,
          reason: error instanceof AppException ? error.message : `Lỗi khi xóa sản phẩm: ${error.message}`,
        });
      }
    }

    this.logger.log(
      `Kết quả xóa sản phẩm: ${successIds.length} thành công, ${failedIds.length} thất bại`,
    );

    return { successIds, failedIds };
  }

  /**
   * Cập nhật trạng thái nhiều sản phẩm
   * @param employeeId ID của nhân viên
   * @param updateMultipleStatusDto DTO cập nhật trạng thái nhiều sản phẩm
   * @returns Danh sách ID sản phẩm đã cập nhật thành công và danh sách ID sản phẩm thất bại
   */
  @Transactional()
  async updateMultipleProductsStatus(
    employeeId: number,
    updateMultipleStatusDto: UpdateMultipleProductsStatusDto,
  ): Promise<{
    successIds: number[];
    failedIds: { id: number; reason: string }[];
  }> {
    const { status, productIds } = updateMultipleStatusDto;
    const successIds: number[] = [];
    const failedIds: { id: number; reason: string }[] = [];

    // Xử lý từng sản phẩm một
    for (const productId of productIds) {
      try {
        // Tìm sản phẩm theo ID
        const product = await this.productRepository.findById(productId);

        // Kiểm tra sản phẩm tồn tại
        if (!product) {
          failedIds.push({
            id: productId,
            reason: `Sản phẩm với ID ${productId} không tồn tại`,
          });
          continue;
        }

        // Kiểm tra quy tắc chuyển trạng thái
        try {
          this.validationHelper.validateProductStatusTransition(
            product,
            employeeId,
            status,
          );
        } catch (error) {
          failedIds.push({
            id: productId,
            reason:
              error instanceof AppException
                ? error.message
                : `Không thể chuyển trạng thái: ${error.message}`,
          });
          continue;
        }

        // Cập nhật trạng thái sản phẩm và xử lý các tác vụ liên quan
        await this.productRepository.updateProductStatusWithRelatedTasks(
          productId,
          status,
        );

        // Thêm vào danh sách thành công
        successIds.push(productId);
      } catch (error) {
        // Thêm vào danh sách thất bại
        failedIds.push({
          id: productId,
          reason:
            error instanceof AppException
              ? error.message
              : `Lỗi khi cập nhật trạng thái: ${error.message}`,
        });
      }
    }

    return { successIds, failedIds };
  }
}
