import { User<PERSON>eyLlm } from '../../entities/user-key-llm.entity';
import { UserModelBaseResponseDto, ModelInfoResponseDto } from '../dto/user-model-base';

/**
 * Mapper cho UserModelBase
 * Chuyển đổi giữa entity và DTO cho user model access
 */
export class UserModelBaseMapper {
  /**
   * Chuyển đổi admin model base entity sang user response DTO
   * @param entity ModelBase entity
   * @returns UserModelBaseResponseDto
   */
  // static toUserResponseDto(entity: ModelBase): UserModelBaseResponseDto {
  //   return {
  //     id: entity.id,
  //     name: entity.name,
  //     modelId: entity.modelId,
  //     provider: entity.provider,
  //     description: entity.description,
  //     status: entity.status,
  //     isUserAccessible: entity.isUserAccessible,
  //     isFineTunable: entity.isFineTunable,
  //     inputCostPer1kTokens: entity.inputCostPer1kTokens,
  //     outputCostPer1kTokens: entity.outputCostPer1kTokens,
  //     source: 'admin',
  //     metadata: entity.metadata,
  //     createdAt: entity.createdAt,
  //     updatedAt: entity.updatedAt,
  //     availability: {
  //       isAvailable: entity.status === 'ACTIVE',
  //       lastChecked: Date.now()
  //     }
  //   };
  // }

  /**
   * Chuyển đổi user key model sang user response DTO
   * @param modelInfo Model info từ API
   * @param userKey User key entity
   * @returns UserModelBaseResponseDto
   */
  // static toUserKeyResponseDto(modelInfo: any, userKey: UserKeyLlm): UserModelBaseResponseDto {
  //   return {
  //     id: `${userKey.id}-${modelInfo.id}`, // Composite ID
  //     name: modelInfo.name || modelInfo.id,
  //     modelId: modelInfo.id,
  //     provider: userKey.provider,
  //     description: modelInfo.description,
  //     status: 'ACTIVE' as any,
  //     isUserAccessible: true,
  //     isFineTunable: modelInfo.capabilities?.includes('fine-tuning') || false,
  //     inputCostPer1kTokens: modelInfo.pricing?.inputCostPer1k,
  //     outputCostPer1kTokens: modelInfo.pricing?.outputCostPer1k,
  //     contextLength: modelInfo.contextLength,
  //     capabilities: modelInfo.capabilities || ['text-generation'],
  //     source: 'user-key',
  //     userKeyInfo: {
  //       keyId: userKey.id,
  //       keyName: userKey.name,
  //       isDefault: userKey.isDefault
  //     },
  //     metadata: modelInfo.metadata,
  //     createdAt: userKey.createdAt,
  //     updatedAt: userKey.updatedAt,
  //     availability: {
  //       isAvailable: true,
  //       lastChecked: Date.now()
  //     }
  //   };
  // }

  /**
   * Chuyển đổi mảng admin models sang mảng user response DTOs
   * @param entities Mảng ModelBase entities
   * @returns Mảng UserModelBaseResponseDto
   */
  // static toUserResponseDtoArray(entities: ModelBase[]): UserModelBaseResponseDto[] {
  //   return entities.map(entity => this.toUserResponseDto(entity));
  // }

  /**
   * Chuyển đổi generic model object sang user response DTO
   * @param model Model object từ bất kỳ source nào
   * @returns UserModelBaseResponseDto
   */
  // static toResponseDto(model: any): UserModelBaseResponseDto {
  //   return {
  //     id: model.id,
  //     name: model.name,
  //     modelId: model.modelId || model.id,
  //     provider: model.provider,
  //     description: model.description,
  //     status: model.status,
  //     isUserAccessible: model.isUserAccessible,
  //     isFineTunable: model.isFineTunable,
  //     inputCostPer1kTokens: model.inputCostPer1kTokens,
  //     outputCostPer1kTokens: model.outputCostPer1kTokens,
  //     contextLength: model.contextWindow || model.contextLength,
  //     capabilities: model.capabilities || ['text-generation'],
  //     source: model.source,
  //     userKeyInfo: model.userKeyId ? {
  //       keyId: model.userKeyId,
  //       keyName: model.userKeyName,
  //       isDefault: false
  //     } : undefined,
  //     metadata: model.metadata,
  //     createdAt: model.createdAt,
  //     updatedAt: model.updatedAt,
  //     availability: {
  //       isAvailable: model.status === 'ACTIVE' || model.status === ModelStatusEnum.ACTIVE,
  //       lastChecked: Date.now()
  //     }
  //   };
  // }

  /**
   * Chuyển đổi model info sang detailed response DTO
   * @param modelInfo Model info từ API hoặc database
   * @param source Source của model
   * @param userKey User key (nếu có)
   * @returns ModelInfoResponseDto
   */
  static toModelInfoResponseDto(
    modelInfo: any,
    source: 'admin' | 'user-key' | 'registry',
    userKey?: UserKeyLlm
  ): ModelInfoResponseDto {
    return {
      modelId: modelInfo.modelId || modelInfo.id,
      name: modelInfo.name,
      provider: modelInfo.provider,
      description: modelInfo.description,
      version: modelInfo.version,
      inputCostPer1kTokens: modelInfo.inputCostPer1kTokens,
      outputCostPer1kTokens: modelInfo.outputCostPer1kTokens,
      contextLength: modelInfo.contextLength,
      maxOutputTokens: modelInfo.maxOutputTokens,
      capabilities: modelInfo.capabilities,
      supportedLanguages: modelInfo.supportedLanguages,
      trainingDataCutoff: modelInfo.trainingDataCutoff,
      parameters: modelInfo.parameters,
      architecture: modelInfo.architecture,
      availability: {
        isAvailable: modelInfo.status === 'ACTIVE' || modelInfo.isAvailable !== false,
        lastChecked: Date.now(),
        responseTime: modelInfo.responseTime,
        status: this.getAvailabilityStatus(modelInfo)
      },
      rateLimits: modelInfo.rateLimits,
      source,
      userKeyInfo: userKey ? {
        keyId: userKey.id,
        keyName: userKey.name,
        lastTested: userKey.updatedAt
      } : undefined,
      recommendations: this.generateRecommendations(modelInfo),
      metadata: modelInfo.metadata
    };
  }

  /**
   * Get availability status từ model info
   * @param modelInfo Model info
   * @returns Availability status
   */
  private static getAvailabilityStatus(modelInfo: any): 'operational' | 'degraded' | 'down' | 'maintenance' {
    if (modelInfo.status === 'INACTIVE' || modelInfo.isAvailable === false) {
      return 'down';
    }
    if (modelInfo.status === 'MAINTENANCE') {
      return 'maintenance';
    }
    if (modelInfo.responseTime && modelInfo.responseTime > 5000) {
      return 'degraded';
    }
    return 'operational';
  }

  /**
   * Generate usage recommendations cho model
   * @param modelInfo Model info
   * @returns Recommendations
   */
  private static generateRecommendations(modelInfo: any): any {
    const recommendations = {
      bestFor: [] as string[],
      notRecommendedFor: [] as string[],
      tips: [] as string[]
    };

    // Generate recommendations based on model capabilities
    if (modelInfo.capabilities?.includes('text-generation')) {
      recommendations.bestFor.push('chat', 'content-generation', 'summarization');
    }
    if (modelInfo.capabilities?.includes('code-generation')) {
      recommendations.bestFor.push('code-generation', 'debugging', 'code-review');
    }
    if (modelInfo.capabilities?.includes('function-calling')) {
      recommendations.bestFor.push('tool-usage', 'api-integration');
      recommendations.tips.push('Use function calling for structured interactions');
    }
    if (modelInfo.capabilities?.includes('image-generation')) {
      recommendations.bestFor.push('image-creation', 'visual-content');
    }

    // Add cost-based recommendations
    if (modelInfo.inputCostPer1kTokens && modelInfo.inputCostPer1kTokens > 0.05) {
      recommendations.notRecommendedFor.push('high-volume-processing');
      recommendations.tips.push('Consider cost optimization for large-scale usage');
    }

    // Add context length recommendations
    if (modelInfo.contextLength && modelInfo.contextLength > 100000) {
      recommendations.bestFor.push('long-document-analysis', 'large-context-tasks');
      recommendations.tips.push('Leverage large context window for comprehensive analysis');
    }

    return recommendations;
  }

  /**
   * Merge admin models và user key models
   * @param adminModels Admin provided models
   * @param userKeyModels User key models
   * @returns Merged và deduplicated models
   */
  static mergeModels(
    adminModels: UserModelBaseResponseDto[],
    userKeyModels: UserModelBaseResponseDto[]
  ): UserModelBaseResponseDto[] {
    const modelMap = new Map<string, UserModelBaseResponseDto>();

    // Add admin models first (lower priority)
    adminModels.forEach(model => {
      const key = `${model.provider}-${model.modelId}`;
      modelMap.set(key, model);
    });

    // Add user key models (higher priority - will override admin models)
    userKeyModels.forEach(model => {
      const key = `${model.provider}-${model.modelId}`;
      modelMap.set(key, model);
    });

    return Array.from(modelMap.values());
  }

  /**
   * Filter models theo query parameters
   * @param models Danh sách models
   * @param query Query parameters
   * @returns Filtered models
   */
  static filterModels(models: UserModelBaseResponseDto[], query: any): UserModelBaseResponseDto[] {
    let filtered = [...models];

    // Filter by name
    if (query.name) {
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes(query.name.toLowerCase()) ||
        model.modelId.toLowerCase().includes(query.name.toLowerCase())
      );
    }

    // Filter by modelId
    if (query.modelId) {
      filtered = filtered.filter(model =>
        model.modelId.toLowerCase().includes(query.modelId.toLowerCase())
      );
    }

    // Filter by provider
    if (query.provider) {
      filtered = filtered.filter(model => model.provider === query.provider);
    }

    // Filter by user accessible
    if (query.isUserAccessible !== undefined) {
      filtered = filtered.filter(model => model.isUserAccessible === query.isUserAccessible);
    }

    // Filter by fine-tunable
    if (query.isFineTunable !== undefined) {
      filtered = filtered.filter(model => model.isFineTunable === query.isFineTunable);
    }

    // Filter by source
    if (query.source) {
      filtered = filtered.filter(model => model.source === query.source);
    }

    // Filter by keyId
    if (query.keyId) {
      filtered = filtered.filter(model =>
        model.source === 'user-key' && model.userKeyInfo?.keyId === query.keyId
      );
    }

    // Filter by capability
    if (query.capability) {
      filtered = filtered.filter(model =>
        model.capabilities?.includes(query.capability)
      );
    }

    // General search
    if (query.search) {
      const searchTerm = query.search.toLowerCase();
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes(searchTerm) ||
        model.modelId.toLowerCase().includes(searchTerm) ||
        model.description?.toLowerCase().includes(searchTerm) ||
        model.capabilities?.some(cap => cap.toLowerCase().includes(searchTerm))
      );
    }

    return filtered;
  }

  /**
   * Sort models theo query parameters
   * @param models Danh sách models
   * @param sortBy Sort field
   * @param sortDirection Sort direction
   * @returns Sorted models
   */
  static sortModels(
    models: UserModelBaseResponseDto[],
    sortBy: string = 'name',
    sortDirection: 'ASC' | 'DESC' = 'ASC'
  ): UserModelBaseResponseDto[] {
    return models.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'provider':
          aValue = a.provider;
          bValue = b.provider;
          break;
        case 'inputCostPer1kTokens':
          aValue = a.inputCostPer1kTokens || 0;
          bValue = b.inputCostPer1kTokens || 0;
          break;
        case 'outputCostPer1kTokens':
          aValue = a.outputCostPer1kTokens || 0;
          bValue = b.outputCostPer1kTokens || 0;
          break;
        case 'contextLength':
          aValue = a.contextLength || 0;
          bValue = b.contextLength || 0;
          break;
        case 'createdAt':
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case 'updatedAt':
          aValue = a.updatedAt;
          bValue = b.updatedAt;
          break;
        default:
          aValue = a.name;
          bValue = b.name;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortDirection === 'ASC' ? comparison : -comparison;
      }

      if (aValue < bValue) return sortDirection === 'ASC' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'ASC' ? 1 : -1;
      return 0;
    });
  }

  /**
   * Apply pagination to models
   * @param models Danh sách models
   * @param page Page number
   * @param limit Items per page
   * @returns Paginated result
   */
  static paginateModels(models: UserModelBaseResponseDto[], page: number, limit: number) {
    const totalItems = models.length;
    const skip = (page - 1) * limit;
    const items = models.slice(skip, skip + limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page
      }
    };
  }
}
