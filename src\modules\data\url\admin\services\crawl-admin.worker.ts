import { Injectable, Logger } from '@nestjs/common';
import { Processor, WorkerHost, OnQueueEvent } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CrawlSession } from '../../entities/crawl-session.entity';
import { Url } from '../../entities/url.entity';
import { CrawlSessionRepository } from '../../repositories/crawl-session.repository';
import { UrlRepository } from '../../repositories/url.repository';
import { QueueName, CrawlUrlJobName } from '@shared/queue/queue.constants';
import { CrawlAdminDto } from '../dto/crawl-admin.dto';
import { AdminAdvancedCrawlerService } from './admin-advanced-crawler.service';
import { ExtractedMetadata } from '../interfaces/extracted-metadata.interface';
import { AppException } from '@/common';
import { URL_ERROR_CODES } from '../../exceptions';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as cheerio from 'cheerio';
import { backOff } from 'exponential-backoff';

// Import types từ user worker để tái sử dụng
interface CrawlProgress {
  totalUrls: number;
  processedUrls: number;
  successfulUrls: number;
  failedUrls: number;
  currentDepth: number;
  currentUrl?: string;
  percentage: number;
  startTime: number;
  estimatedTimeRemaining?: number;
}

enum CrawlErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

interface CrawlError {
  type: CrawlErrorType;
  message: string;
  url: string;
  statusCode?: number;
  retryable?: boolean;
  retryAfter?: number;
}

/**
 * Worker xử lý crawl URL cho Admin
 * Tương tự như user worker nhưng dành riêng cho admin
 */
@Injectable()
@Processor(QueueName.CRAWL_URL_ADMIN, {
  concurrency: 3, // ✅ Xử lý tối đa 3 job admin đồng thời (ít hơn user để ưu tiên user)
})
export class CrawlAdminWorker extends WorkerHost {
  private readonly logger = new Logger(CrawlAdminWorker.name);

  // Cache và rate limiting (tương tự user worker)
  private readonly robotsCache = new Map<string, { allowed: boolean; expiry: number }>();
  private readonly dnsCache = new Map<string, { ip: string; expiry: number }>();
  private readonly metadataCache = new Map<string, { metadata: ExtractedMetadata; expiry: number }>();
  private readonly domainRateLimits = new Map<string, { lastRequest: number; requestCount: number; resetTime: number }>();
  private readonly websiteAnalysisCache = new Map<string, { type: any; confidence: number; cachedAt: number }>();
  private readonly domainSuccessRates = new Map<string, { successCount: number; totalCount: number; lastUpdated: number }>();
  private readonly progressCallbacks = new Map<string, (progress: CrawlProgress) => void>();

  // Constants (tương tự user worker nhưng conservative hơn cho admin)
  private readonly CONCURRENCY_LIMIT = 8; // ✅ Xử lý 8 URLs song song trong 1 admin job
  private readonly MIN_DELAY_BETWEEN_REQUESTS = 100; // ✅ 100ms delay giữa các requests
  private readonly MAX_REQUESTS_PER_MINUTE = 30; // ✅ Tối đa 30 requests/phút
  private readonly RATE_LIMIT_WINDOW = 60 * 1000;
  private readonly ROBOTS_CACHE_TTL = 24 * 60 * 60 * 1000;
  private readonly DNS_CACHE_TTL = 60 * 60 * 1000;
  private readonly METADATA_CACHE_TTL = 60 * 60 * 1000;
  private readonly WEBSITE_ANALYSIS_CACHE_TTL = 24 * 60 * 60 * 1000;

  constructor(
    @InjectRepository(CrawlSession)
    private readonly crawlSessionRepository: Repository<CrawlSession>,
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    private readonly crawlSessionCustomRepository: CrawlSessionRepository,
    private readonly urlCustomRepository: UrlRepository,
    private readonly adminAdvancedCrawlerService: AdminAdvancedCrawlerService,
    private readonly httpService: HttpService,
    @InjectQueue(QueueName.CRAWL_URL_ADMIN)
    private readonly crawlAdminQueue: Queue,
  ) {
    super();
    this.logger.log('🚀 CrawlAdminWorker initialized successfully');

    // ✅ Setup worker event listeners trước khi khởi tạo connection
    this.setupAdminWorkerEventListeners();

    // ✅ Kiểm tra và đợi Redis connection sẵn sàng cho admin worker
    this.initializeAdminWorkerConnection();

    // Cleanup cache mỗi 30 phút
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 30 * 60 * 1000);
  }

  /**
   * ✅ Setup admin worker event listeners để debug và monitor worker lifecycle
   */
  private setupAdminWorkerEventListeners(): void {
    // Sử dụng setTimeout để setup events sau khi worker được khởi tạo hoàn toàn
    setTimeout(() => {
      try {
        // Perform admin worker warm-up ngay lập tức
        this.performAdminWorkerWarmup();
      } catch (error) {
        this.logger.error(`❌ Error setting up admin worker events: ${error.message}`);
      }
    }, 1000);
  }

  /**
   * ✅ Perform admin worker warm-up để đảm bảo worker thực sự sẵn sàng
   */
  private async performAdminWorkerWarmup(): Promise<void> {
    try {
      // Đợi một chút để đảm bảo tất cả connections đã ổn định
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Kiểm tra queue operations
      const waitingJobs = await this.crawlAdminQueue.getWaiting();
      const activeJobs = await this.crawlAdminQueue.getActive();

      // Chỉ log nếu có jobs đang chờ (cần debug)
      if (waitingJobs.length > 0) {
        this.logger.log(`🔍 Found ${waitingJobs.length} waiting jobs after admin warm-up`);
        waitingJobs.slice(0, 3).forEach(job => {
          this.logger.log(`📋 Admin waiting job: ${job.id} (${job.name}) - added at ${new Date(job.timestamp)}`);
        });

        // Trigger manual check cho waiting jobs nếu cần
        this.logger.log('🔄 Triggering manual check for admin waiting jobs...');
        setTimeout(async () => {
          const stillWaiting = await this.crawlAdminQueue.getWaiting();
          if (stillWaiting.length > 0) {
            this.logger.warn(`⚠️ Admin still have ${stillWaiting.length} waiting jobs after warm-up`);
          }
        }, 5000);
      }

    } catch (error) {
      this.logger.error(`❌ Admin worker warm-up failed: ${error.message}`);
    }
  }

  /**
   * ✅ Khởi tạo và kiểm tra Redis connection cho admin worker
   * Đây là giải pháp cho vấn đề worker không xử lý job đầu tiên sau restart
   */
  private async initializeAdminWorkerConnection(): Promise<void> {
    try {
      this.logger.log('🔄 Đang kiểm tra Redis connection cho admin worker...');

      // ✅ Sử dụng cách tiếp cận khác thay vì truy cập trực tiếp connection
      // Kiểm tra queue có sẵn sàng bằng cách thử thực hiện một operation đơn giản
      const waitingJobs = await this.crawlAdminQueue.getWaiting();

      // Chỉ log nếu có waiting jobs (cần debug)
      if (waitingJobs.length > 0) {
        this.logger.log(`🔧 Admin worker ready - waiting jobs: ${waitingJobs.length}`);
      }

      // ✅ Thêm periodic health check cho admin worker
      // this.startAdminPeriodicHealthCheck(); // Đã bỏ health check log

    } catch (error) {
      this.logger.error(`❌ Lỗi khi khởi tạo Redis connection cho admin worker: ${error.message}`);

      // Thử đợi một chút và retry
      try {
        this.logger.log('🔄 Retry kiểm tra connection cho admin worker...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        await this.crawlAdminQueue.getWaiting();
        this.logger.log('✅ Redis connection đã sẵn sàng cho admin worker sau retry');

        // ✅ Thêm periodic health check sau retry thành công
        // this.startAdminPeriodicHealthCheck(); // Đã bỏ health check log
      } catch (retryError) {
        this.logger.error(`❌ Retry thất bại cho admin worker: ${retryError.message}`);
        this.logger.error('⚠️ Admin worker có thể không xử lý job đầu tiên đúng cách');

        // Không throw error để không crash worker, chỉ log warning
        // Worker vẫn có thể hoạt động sau khi connection ổn định
      }
    }
  }

  /**
   * ✅ Start periodic health check cho admin worker để monitor worker state
   */
  private startAdminPeriodicHealthCheck(): void {
    setInterval(async () => {
      try {
        const waitingJobs = await this.crawlAdminQueue.getWaiting();
        const activeJobs = await this.crawlAdminQueue.getActive();
        const completedJobs = await this.crawlAdminQueue.getCompleted();
        const failedJobs = await this.crawlAdminQueue.getFailed();

        // Chỉ log khi có jobs hoặc mỗi 5 phút
        const now = Date.now();
        if (waitingJobs.length > 0 || activeJobs.length > 0 || now % (5 * 60 * 1000) < 30000) {
          this.logger.log(`🏥 Admin health check - waiting: ${waitingJobs.length}, active: ${activeJobs.length}, completed: ${completedJobs.length}, failed: ${failedJobs.length}`);
        }

        // Cảnh báo nếu có jobs chờ quá lâu
        if (waitingJobs.length > 0) {
          const oldestJob = waitingJobs[0];
          const waitTime = now - oldestJob.timestamp;
          if (waitTime > 60000) { // 1 phút
            this.logger.warn(`⚠️ Admin job ${oldestJob.id} đã chờ ${Math.round(waitTime/1000)}s`);
          }
        }

      } catch (error) {
        this.logger.error(`❌ Admin health check failed: ${error.message}`);
      }
    }, 30000); // Check mỗi 30 giây
  }

  @OnQueueEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`❌ Admin job ${job.id} failed: ${error.message}`);
  }

  @OnQueueEvent('active')
  onActive(job: Job) {
    this.logger.log(`🚀 Admin job ${job.id} started processing (concurrent admin jobs active)`);
  }

  @OnQueueEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`✅ Admin job ${job.id} completed successfully`);
  }

  @OnQueueEvent('stalled')
  onStalled(job: Job) {
    this.logger.warn(`⚠️ Admin job ${job.id} stalled and will be retried`);
  }

  /**
   * Xử lý job crawl URL cho admin
   * @param job Bull job với data crawl
   */
  async process(job: Job<{employeeId: number, crawlDto: CrawlAdminDto, sessionId?: string, cancelled?: boolean}>) {
    console.log(`🚀 CrawlAdminWorker: Bắt đầu xử lý job ${job.name} với ID: ${job.id}`);
    this.logger.log(`🚀 CrawlAdminWorker: Bắt đầu xử lý job ${job.name} với ID: ${job.id}`);
    this.logger.log(`📊 Admin job details: name=${job.name}, id=${job.id}, data=${JSON.stringify(job.data)}`);

    try {
      switch (job.name) {
        case CrawlUrlJobName.CRAWL_URL_ADMIN:
          console.log(`✅ Processing CRAWL_URL_ADMIN job`);
          this.logger.log(`✅ Processing CRAWL_URL_ADMIN job`);
          return await this.handleCrawlUrlAdmin(job);
        default:
          console.log(`❌ Unsupported admin job name: ${job.name}`);
          this.logger.error(`❌ Unsupported admin job name: ${job.name}`);
          throw new Error(`Không hỗ trợ job name: ${job.name}`);
      }
    } catch (error) {
      console.log(`❌ Lỗi khi xử lý admin job ${job.name}: ${error.message}`);
      this.logger.error(`❌ Lỗi khi xử lý admin job ${job.name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async handleCrawlUrlAdmin(job: Job<{employeeId: number, crawlDto: CrawlAdminDto, sessionId?: string, cancelled?: boolean}>) {
    const { employeeId, crawlDto, sessionId, cancelled } = job.data;
    this.logger.log(`📋 Admin job data: employeeId=${employeeId}, url=${crawlDto.url}, sessionId=${sessionId}, cancelled=${cancelled}`);

    // Kiểm tra job có bị hủy không ngay từ đầu
    if (cancelled) {
      this.logger.log(`🛑 Admin job ${job.id} đã bị hủy, không thực hiện crawl`);
      return {
        status: 'cancelled',
        message: 'Job đã bị hủy bởi admin',
        urlsProcessed: 0,
        urlsSaved: 0,
      };
    }

    try {
      // Nếu có sessionId, cập nhật session trước khi bắt đầu
      if (sessionId) {
        const maxUrls = crawlDto.maxUrls || 20;
        await this.updateSessionProgress(sessionId, {
          totalUrls: maxUrls,
          processedUrls: 0,
          successfulUrls: 0,
          failedUrls: 0,
          currentDepth: 0,
          percentage: 0,
        });
      }

      // Thực hiện crawl (sử dụng employeeId thay vì userId)
      const result = await this.crawlUrl(employeeId, crawlDto, sessionId, job);

      // ✅ Kiểm tra xem session có bị hủy không trước khi cập nhật kết quả
      if (sessionId && result.status !== 'cancelled') {
        // Kiểm tra trạng thái session hiện tại
        const currentSession = await this.crawlSessionCustomRepository.findSessionById(sessionId);
        if (currentSession && currentSession.status === 'cancelled') {
          this.logger.log(`🛑 Admin session ${sessionId} đã bị hủy, không cập nhật kết quả thành completed`);
          return {
            status: 'cancelled',
            message: 'Session đã bị hủy bởi admin',
            urlsProcessed: result.urlsProcessed || 0,
            urlsSaved: result.urlsSaved || 0,
          };
        }

        // Chỉ cập nhật nếu session chưa bị hủy
        const status = result.status === 'success' ? 'completed' : 'error';
        await this.updateSessionResult(sessionId, status, {
          urlsProcessed: result.urlsProcessed || 0,
          urlsSaved: result.urlsSaved || 0,
          message: result.message,
          errors: result.errors,
        });
      }

      return result;
    } catch (error) {
      // Cập nhật session với lỗi nếu có (chỉ khi chưa bị hủy)
      if (sessionId) {
        const currentSession = await this.crawlSessionCustomRepository.findSessionById(sessionId);
        if (!currentSession || currentSession.status !== 'cancelled') {
          await this.updateSessionResult(sessionId, 'error', {
            urlsProcessed: 0,
            urlsSaved: 0,
            message: error.message || 'Lỗi không xác định',
            errors: [error.message || 'Lỗi không xác định'],
          });
        }
      }
      throw error;
    }
  }

  /**
   * Kiểm tra xem crawl có bị hủy không
   * @param sessionId ID session
   * @param job Bull job
   * @returns true nếu bị hủy
   */
  private async checkCancellation(sessionId?: string, job?: Job): Promise<boolean> {
    // ✅ Ưu tiên kiểm tra session trước (nhanh hơn và chính xác hơn)
    if (sessionId) {
      try {
        const currentSession = await this.crawlSessionCustomRepository.findSessionById(sessionId);
        if (!currentSession || currentSession.status === 'cancelled') {
          this.logger.log(`🛑 Admin Session ${sessionId} đã bị hủy hoặc không tồn tại`);
          return true;
        }
      } catch (error) {
        this.logger.warn(`⚠️ Không thể kiểm tra admin session data: ${error.message}`);
      }
    }

    // ✅ Kiểm tra job data (backup check)
    if (job && job.id) {
      try {
        const currentJob = await this.crawlAdminQueue.getJob(String(job.id));
        if (currentJob && currentJob.data.cancelled) {
          this.logger.log(`🛑 Admin Job ${job.id} đã bị đánh dấu cancelled`);
          return true;
        }
      } catch (error) {
        this.logger.warn(`⚠️ Không thể kiểm tra admin job data: ${error.message}`);
      }
    }

    return false;
  }

  /**
   * Cleanup expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();

    // Cleanup robots cache
    for (const [domain, cache] of this.robotsCache.entries()) {
      if (cache.expiry < now) {
        this.robotsCache.delete(domain);
      }
    }

    // Cleanup DNS cache
    for (const [hostname, cache] of this.dnsCache.entries()) {
      if (cache.expiry < now) {
        this.dnsCache.delete(hostname);
      }
    }

    // Cleanup metadata cache
    for (const [url, cache] of this.metadataCache.entries()) {
      if (cache.expiry < now) {
        this.metadataCache.delete(url);
      }
    }

    // Cleanup rate limit data
    for (const [domain, rateLimit] of this.domainRateLimits.entries()) {
      if (rateLimit.resetTime < now) {
        this.domainRateLimits.delete(domain);
      }
    }

    // Cleanup website analysis cache
    for (const [domain, cache] of this.websiteAnalysisCache.entries()) {
      if (cache.cachedAt + this.WEBSITE_ANALYSIS_CACHE_TTL < now) {
        this.websiteAnalysisCache.delete(domain);
      }
    }

    // Cleanup success rate cache
    for (const [domain, successRate] of this.domainSuccessRates.entries()) {
      if (now - successRate.lastUpdated > 60 * 60 * 1000) {
        this.domainSuccessRates.delete(domain);
      }
    }

    this.logger.debug(
      `Admin cache cleanup completed. Robots: ${this.robotsCache.size}, DNS: ${this.dnsCache.size}, Metadata: ${this.metadataCache.size}`,
    );
  }

  /**
   * Crawl URL và các URL con để lấy metadata từ thẻ head với xử lý song song và progress tracking (Admin)
   * @param employeeId ID của admin
   * @param crawlDto Thông tin URL cần crawl
   * @param sessionId ID session để track progress (optional)
   * @param job Bull job instance để kiểm tra cancellation
   * @returns Danh sách các URL với metadata
   */
  async crawlUrl(
    employeeId: number,
    crawlDto: CrawlAdminDto,
    sessionId?: string,
    job?: Job,
  ): Promise<{
    status: string;
    message: string;
    urlsProcessed?: number;
    urlsSaved?: number;
    errors?: string[];
  }> {
    // Mảng lưu trữ các lỗi gặp phải
    const errors: string[] = [];
    const processedUrls: ExtractedMetadata[] = [];
    const visitedUrls = new Set<string>();
    const urlsToVisit: Array<{ url: string; depth: number }> = [
      { url: crawlDto.url, depth: 0 },
    ];

    const MAX_URLS = crawlDto.maxUrls || 20;
    const trackingSessionId = sessionId || `crawl_admin_${employeeId}_${Date.now()}`;

    // Progress tracking
    const progress: CrawlProgress = {
      totalUrls: MAX_URLS,
      processedUrls: 0,
      successfulUrls: 0,
      failedUrls: 0,
      currentDepth: 0,
      percentage: 0,
      startTime: Date.now(),
    };

    try {
      this.logger.log(`===== BẮT ĐẦU CRAWL URL ADMIN [${trackingSessionId}] =====`);
      this.logger.log(
        `Admin: ${employeeId}, URL: ${crawlDto.url}, Độ sâu: ${crawlDto.depth}, Max URLs: ${MAX_URLS}`,
      );

      // Cập nhật progress ban đầu
      if (sessionId) {
        this.logger.log(`🔄 Cập nhật progress ban đầu cho admin session ${sessionId}`);
        await this.updateProgress(sessionId, progress);
      }

      // ✅ Biến theo dõi trạng thái hủy
      let isCancelled = false;

      // Crawl URLs với xử lý song song
      while (urlsToVisit.length > 0 && visitedUrls.size < MAX_URLS && !isCancelled) {
        // ✅ Sử dụng helper function để kiểm tra hủy
        isCancelled = await this.checkCancellation(sessionId, job);
        if (isCancelled) {
          this.logger.log(`🛑 Admin crawl đã bị hủy, dừng vòng lặp chính`);
          break;
        }

        // Lấy batch URLs để xử lý song song
        const currentBatch: Array<{ url: string; depth: number }> = [];

        // Lấy tối đa CONCURRENCY_LIMIT URLs chưa được xử lý
        while (
          currentBatch.length < this.CONCURRENCY_LIMIT &&
          urlsToVisit.length > 0
        ) {
          const currentItem = urlsToVisit.shift();
          if (!currentItem) continue;

          const { url } = currentItem;

          // Bỏ qua nếu URL đã được xử lý
          if (visitedUrls.has(url)) continue;

          // Đánh dấu URL đã được xử lý
          visitedUrls.add(url);
          currentBatch.push(currentItem);
        }

        if (currentBatch.length === 0) break;

        // Cập nhật current depth và URL
        progress.currentDepth = Math.max(
          ...currentBatch.map((item) => item.depth),
        );
        progress.currentUrl = currentBatch[0].url;

        this.logger.log(
          `Admin xử lý batch ${currentBatch.length} URLs song song (depth ${progress.currentDepth})`,
        );

        // Xử lý batch URLs song song với smart crawling
        const batchResults = await this.processConcurrentUrlsWithSmartCrawling(
          currentBatch,
          employeeId,
          crawlDto,
          visitedUrls,
          urlsToVisit,
          errors,
          sessionId,
          job,
        );

        // Cập nhật progress
        progress.processedUrls = visitedUrls.size;
        progress.successfulUrls += batchResults.length;
        progress.failedUrls = progress.processedUrls - progress.successfulUrls;

        // Thêm kết quả thành công vào danh sách
        processedUrls.push(...batchResults);

        this.logger.log(
          `Admin hoàn thành batch: ${batchResults.length}/${currentBatch.length} URLs thành công`,
        );

        // ✅ Kiểm tra hủy sau khi xử lý batch
        isCancelled = await this.checkCancellation(sessionId, job);
        if (isCancelled) {
          this.logger.log(`🛑 Admin crawl đã bị hủy sau khi xử lý batch, dừng ngay lập tức`);
          break;
        }

        // Batch progress updates (mỗi 5 URLs hoặc 10% thay vì mỗi batch)
        if (sessionId) {
          const percentage = Math.min(
            Math.round((progress.processedUrls / progress.totalUrls) * 100),
            100
          );

          const shouldUpdate =
            progress.processedUrls % 5 === 0 ||
            percentage % 10 === 0 ||
            visitedUrls.size >= MAX_URLS ||
            urlsToVisit.length === 0;

          if (shouldUpdate) {
            this.logger.log(`🔄 Cập nhật admin progress batch cho session ${sessionId}: ${progress.processedUrls}/${progress.totalUrls} (${percentage}%)`);
            await this.updateProgress(sessionId, progress);
          } else {
            this.logger.debug(`⏭️ Skip admin progress update: ${progress.processedUrls}/${progress.totalUrls} (${percentage}%)`);
          }
        }
      }

      // ✅ Kiểm tra nếu bị hủy, return với status cancelled
      if (isCancelled) {
        const cancelledMessage = `Admin crawl đã bị hủy. Đã xử lý ${progress.processedUrls} URL, lưu được ${processedUrls.length} URL có metadata.`;

        this.logger.log(`===== ADMIN CRAWL BỊ HỦY [${trackingSessionId}] =====`);
        this.logger.log(cancelledMessage);

        // ❌ KHÔNG cập nhật progress khi bị cancelled - giữ nguyên status "cancelled" trong DB
        this.logger.log(`🚫 Admin crawl bị hủy - không cập nhật progress, giữ nguyên status "cancelled" trong database`);

        // Browser cleanup
        try {
          this.logger.log(`🧹 Performing admin browser cleanup after cancellation`);
          await this.adminAdvancedCrawlerService.closeBrowser();
        } catch (cleanupError) {
          this.logger.warn(`Admin browser cleanup warning: ${cleanupError.message}`);
        }

        return {
          status: 'cancelled',
          message: cancelledMessage,
          urlsProcessed: progress.processedUrls,
          urlsSaved: processedUrls.length,
          errors: errors.length > 0 ? errors : undefined,
        };
      }

      // Tạo thông báo kết quả cho trường hợp hoàn thành bình thường
      const totalCrawledUrls = progress.processedUrls;
      let resultMessage = '';
      if (totalCrawledUrls > 0) {
        const savedCount = processedUrls.length;
        if (savedCount > 0) {
          resultMessage = `Admin đã crawl thành công ${totalCrawledUrls} URL từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs). Đã lưu ${savedCount} URL có metadata vào database.`;
        } else {
          resultMessage = `Admin đã crawl thành công ${totalCrawledUrls} URL từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs). Không có metadata để lưu vào database (có thể do anti-bot protection).`;
        }
      } else {
        resultMessage = `Admin không crawl được URL nào từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs)`;
      }

      this.logger.log(`===== KẾT THÚC ADMIN CRAWL URL [${trackingSessionId}] =====`);
      this.logger.log(resultMessage);

      // Final progress update
      if (sessionId) {
        progress.currentUrl = undefined;
        this.logger.log(`🔄 Cập nhật admin progress cuối cùng cho session ${sessionId}`);
        await this.updateProgress(sessionId, progress);
      }

      // ✅ KHÔNG đóng browser sau mỗi job để tránh conflict với jobs khác
      // Browser sẽ được cleanup tự động khi ứng dụng shutdown
      this.logger.log(`🧹 Admin crawl completed - keeping browser alive for other jobs`);

      return {
        status: 'success',
        message: resultMessage,
        urlsProcessed: totalCrawledUrls,
        urlsSaved: processedUrls.length,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      // Xử lý lỗi
      const errorMsg =
        error instanceof AppException
          ? error.message
          : `Lỗi không xác định: ${error.message}`;
      this.logger.error(`Admin crawl URL thất bại: ${errorMsg}`);

      // Emergency browser cleanup on error
      try {
        this.logger.log(`🚨 Emergency admin browser cleanup due to error`);
        await this.adminAdvancedCrawlerService.closeBrowser();
      } catch (cleanupError) {
        this.logger.warn(`Emergency admin cleanup warning: ${cleanupError.message}`);
      }

      return {
        status: 'error',
        message: errorMsg,
        urlsProcessed: 0,
        errors: errors.length > 0 ? [...errors, errorMsg] : [errorMsg],
      };
    }
  }



  /**
   * Xử lý batch URLs song song với smart crawling cho admin
   * @param batch Batch URLs cần xử lý
   * @param employeeId ID của admin
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @param sessionId ID session để kiểm tra hủy
   * @param job Bull job để kiểm tra hủy
   * @returns Array metadata đã trích xuất
   */
  private async processConcurrentUrlsWithSmartCrawling(
    batch: Array<{ url: string; depth: number }>,
    employeeId: number,
    crawlDto: CrawlAdminDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{ url: string; depth: number }>,
    errors: string[],
    sessionId?: string,
    job?: Job,
  ): Promise<ExtractedMetadata[]> {
    const results: ExtractedMetadata[] = [];

    // ✅ Kiểm tra hủy trước khi bắt đầu xử lý batch
    const isCancelled = await this.checkCancellation(sessionId, job);
    if (isCancelled) {
      this.logger.log(`🛑 Admin batch processing cancelled for session ${sessionId}`);
      return results; // Return empty results nếu bị hủy
    }

    // Xử lý song song với Promise.allSettled để không bị block bởi lỗi
    const promises = batch.map(({ url, depth }) =>
      this.processSingleUrlWithSmartCrawling(
        url,
        depth,
        employeeId,
        crawlDto,
        visitedUrls,
        urlsToVisit,
        errors,
        sessionId, // ✅ Truyền sessionId
        job, // ✅ Truyền job
      ),
    );

    const settledResults = await Promise.allSettled(promises);

    // Xử lý kết quả
    settledResults.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      } else if (result.status === 'rejected') {
        const errorMsg = `Admin lỗi xử lý URL ${batch[index].url}: ${result.reason}`;
        this.logger.warn(errorMsg);
        errors.push(errorMsg);
      }
    });

    return results;
  }

  /**
   * Xử lý một URL đơn lẻ với smart crawling và metadata extraction cho admin
   * @param url URL cần xử lý
   * @param depth Độ sâu hiện tại
   * @param employeeId ID của admin
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @param sessionId ID session để kiểm tra hủy
   * @param job Bull job để kiểm tra hủy
   * @returns Metadata đã trích xuất hoặc null nếu thất bại
   */
  private async processSingleUrlWithSmartCrawling(
    url: string,
    depth: number,
    employeeId: number,
    crawlDto: CrawlAdminDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{ url: string; depth: number }>,
    errors: string[],
    sessionId?: string,
    job?: Job,
  ): Promise<ExtractedMetadata | null> {
    try {
      this.logger.log(`🧠 Admin smart processing URL: ${url} (độ sâu: ${depth})`);

      // ✅ Kiểm tra hủy trước khi xử lý URL
      const isCancelled = await this.checkCancellation(sessionId, job);
      if (isCancelled) {
        this.logger.log(`🛑 Admin URL processing cancelled for ${url} in session ${sessionId}`);
        return null; // Return null nếu bị hủy
      }

      // Kiểm tra metadata cache trước
      const normalizedUrl = this.normalizeUrl(url);
      const cached = this.metadataCache.get(normalizedUrl);
      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Admin metadata cache hit for URL: ${normalizedUrl}`);

        // Vẫn cần trích xuất child URLs nếu chưa đạt độ sâu tối đa
        if (depth < crawlDto.depth) {
          try {
            const childUrls = await this.extractChildUrls(url);
            for (const childUrl of childUrls) {
              if (
                !visitedUrls.has(childUrl) &&
                urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)
              ) {
                urlsToVisit.push({ url: childUrl, depth: depth + 1 });
              }
            }
          } catch (childError) {
            this.logger.warn(
              `Admin lỗi khi trích xuất child URLs từ cache cho ${url}: ${childError.message}`,
            );
          }
        }

        return cached.metadata;
      }

      // Rate limiting
      await this.waitForRateLimit(url);

      // Sử dụng advanced crawler để lấy metadata và URLs
      const crawlResult = await this.smartCrawlWithAdvancedCrawler(url);

      // Trích xuất metadata từ HTML
      const metadata = await this.extractMetadataFromHtml(crawlResult.html, url);

      if (metadata) {
        // Cache metadata
        this.metadataCache.set(normalizedUrl, {
          metadata,
          expiry: Date.now() + this.METADATA_CACHE_TTL,
        });

        // Lưu vào database với employeeId
        await this.saveUrlToDatabase(metadata, employeeId);

        this.logger.log(`✅ Admin đã lưu metadata cho URL: ${url}`);
      }

      // Nếu chưa đạt độ sâu tối đa, trích xuất các URL con
      if (depth < crawlDto.depth) {
        try {
          // Sử dụng URLs từ smart crawl result nếu có
          let childUrls: string[] = [];
          if (crawlResult.urls && crawlResult.urls.length > 0) {
            childUrls = crawlResult.urls;
            this.logger.log(
              `Admin smart crawl extracted ${childUrls.length} child URLs from ${url}`,
            );
          } else {
            // Fallback: trích xuất từ HTML
            childUrls = await this.extractChildUrls(url);
            this.logger.log(
              `Admin fallback extraction: ${childUrls.length} child URLs from ${url}`,
            );
          }

          // Thêm các URL con vào hàng đợi
          for (const childUrl of childUrls) {
            if (
              !visitedUrls.has(childUrl) &&
              urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)
            ) {
              urlsToVisit.push({ url: childUrl, depth: depth + 1 });
            }
          }
        } catch (childError) {
          this.logger.warn(`Admin lỗi khi trích xuất child URLs: ${childError.message}`);
          errors.push(`Admin lỗi trích xuất child URLs từ ${url}: ${childError.message}`);
        }
      }

      return metadata;
    } catch (error) {
      const errorMsg = `Admin lỗi khi xử lý URL ${url}: ${error.message}`;
      this.logger.error(errorMsg);
      errors.push(errorMsg);
      return null;
    }
  }



  /**
   * Smart crawling với advanced crawler cho admin
   * @param url URL cần crawl
   * @returns HTML content và URLs
   */
  private async smartCrawlWithAdvancedCrawler(url: string): Promise<{ html: string; urls: string[] }> {
    try {
      this.logger.log(`🧠 Admin smart crawling: ${url}`);

      // Sử dụng admin advanced crawler để lấy metadata và URLs
      const result = await this.adminAdvancedCrawlerService.crawlWithBrowser(url, {
        waitTime: 3000,
        scrollToBottom: false,
        extractUrls: true,
        takeScreenshot: false,
      });

      return {
        html: result.html || '',
        urls: result.urls || [],
      };
    } catch (error) {
      this.logger.warn(
        `Admin smart crawl failed for ${url}, falling back to traditional method: ${error.message}`,
      );

      // Fallback to traditional HTTP method
      try {
        const html = await this.fetchOptimalHtml(url);
        const urls = await this.extractChildUrlsFromHtml(html, url);
        return { html, urls };
      } catch (fallbackError) {
        this.logger.error(`Admin fallback crawl also failed for ${url}: ${fallbackError.message}`);
        return { html: '', urls: [] };
      }
    }
  }

  /**
   * Trích xuất metadata từ HTML cho admin
   * @param html HTML content
   * @param url URL gốc
   * @returns Metadata đã trích xuất
   */
  private async extractMetadataFromHtml(html: string, url: string): Promise<ExtractedMetadata | null> {
    if (!html || html.trim().length === 0) {
      this.logger.warn(`Admin: No HTML content to extract metadata from for URL: ${url}`);
      return null;
    }

    try {
      const metadata = this.extractHeadMetadata(html, url);

      // Validate metadata
      if (!metadata.title && !metadata.content) {
        this.logger.warn(`Admin: No meaningful metadata found for URL: ${url}`);
        return null;
      }

      // Clean up metadata
      const cleanedMetadata = this.cleanupMetadata(metadata);

      this.logger.debug(
        `Admin extracted metadata: title="${cleanedMetadata.title}", content="${cleanedMetadata.content?.substring(0, 50)}...", tags="${cleanedMetadata.tags}"`,
      );

      return cleanedMetadata;
    } catch (error) {
      this.logger.error(`Admin error extracting metadata from ${url}: ${error.message}`);
      return null;
    }
  }

  /**
   * Lưu metadata vào database cho admin
   * @param metadata Metadata cần lưu
   * @param employeeId ID của admin
   */
  private async saveUrlToDatabase(metadata: ExtractedMetadata, employeeId: number): Promise<void> {
    try {
      this.logger.debug(`Admin saving metadata for URL: ${metadata.url}`);

      // Kiểm tra URL đã tồn tại chưa
      const existingUrl = await this.urlRepository.findOne({
        where: { url: metadata.url },
      });

      const currentTime = Date.now();

      if (existingUrl) {
        // Cập nhật URL đã tồn tại
        existingUrl.title = metadata.title;
        existingUrl.content = metadata.content;
        existingUrl.tags = metadata.tags;
        existingUrl.updatedAt = currentTime;
        await this.urlRepository.save(existingUrl);
        this.logger.debug(`Admin updated existing URL: ${metadata.url}`);
      } else {
        // Tạo URL mới với employeeId (admin không có ownedBy)
        const newUrl = new Url();
        newUrl.url = metadata.url;
        newUrl.title = metadata.title;
        newUrl.content = metadata.content;
        newUrl.tags = metadata.tags;
        newUrl.ownedBy = employeeId; // Sử dụng employeeId thay vì null
        newUrl.createdAt = currentTime;
        newUrl.updatedAt = currentTime;
        await this.urlRepository.save(newUrl);
        this.logger.debug(`Admin created new URL: ${metadata.url}`);
      }
    } catch (error) {
      this.logger.error(`Admin error saving URL to database: ${error.message}`);
      throw error;
    }
  }

  /**
   * Trích xuất child URLs cho admin
   * @param url URL cần trích xuất
   * @returns Danh sách child URLs
   */
  private async extractChildUrls(url: string): Promise<string[]> {
    try {
      // Sử dụng smart crawling
      const result = await this.smartCrawlWithAdvancedCrawler(url);

      if (result.urls && result.urls.length > 0) {
        // Nếu smart crawl đã trích xuất URLs, sử dụng kết quả đó
        this.logger.log(
          `Admin smart crawl extracted ${result.urls.length} URLs from ${url}`,
        );
        return result.urls;
      } else {
        // Fallback: sử dụng HTML extraction
        this.logger.log(`Admin fallback: extracting URLs from HTML for ${url}`);
        const normalUrls = await this.extractChildUrlsFromHtml(result.html, url);
        const aggressiveUrls = await this.extractChildUrlsAggressive(result.html, url);

        // Combine và deduplicate
        const allUrls = [...new Set([...normalUrls, ...aggressiveUrls])];
        this.logger.log(
          `Admin combined extraction: ${normalUrls.length} normal + ${aggressiveUrls.length} aggressive = ${allUrls.length} total URLs`,
        );
        return allUrls;
      }
    } catch (error) {
      this.logger.error(`Admin error extracting child URLs: ${error.message}`);
      return [];
    }
  }

  /**
   * Trích xuất metadata từ thẻ head cho admin
   * @param html HTML của trang web
   * @param url URL của trang web
   * @returns Metadata đã trích xuất
   */
  private extractHeadMetadata(html: string, url: string): ExtractedMetadata {
    const $ = cheerio.load(html);

    // Trích xuất title từ nhiều nguồn
    let title = $('title').text().trim();
    if (!title) {
      title =
        $('meta[property="og:title"]').attr('content') ||
        $('meta[name="twitter:title"]').attr('content') ||
        $('meta[itemprop="name"]').attr('content') ||
        $('h1').first().text().trim() ||
        '';
    }

    // Trích xuất content (description) từ nhiều nguồn
    let content = $('meta[name="description"]').attr('content') || '';
    if (!content) {
      content =
        $('meta[property="og:description"]').attr('content') ||
        $('meta[name="twitter:description"]').attr('content') ||
        $('meta[itemprop="description"]').attr('content') ||
        $('meta[property="description"]').attr('content') ||
        '';
    }

    // Trích xuất tags (keywords) từ nhiều nguồn
    const tags =
      $('meta[name="keywords"]').attr('content') ||
      $('meta[property="article:tag"]').attr('content') ||
      $('meta[property="keywords"]').attr('content') ||
      '';

    this.logger.debug(
      `Admin extracted metadata from head: title="${title}", content="${content.substring(0, 50)}...", tags="${tags}"`,
    );

    return {
      url,
      title,
      content,
      tags,
    };
  }

  /**
   * Clean up metadata để đảm bảo tags không bị lưu vào content cho admin
   * @param metadata Metadata cần clean up
   * @returns Metadata đã được clean up
   */
  private cleanupMetadata(metadata: ExtractedMetadata): ExtractedMetadata {
    if (!metadata.content) {
      return metadata;
    }

    // Kiểm tra và loại bỏ tags từ cuối content nếu có
    const tagsPattern = /\n\nTags:\s*(.+)$/;
    const match = metadata.content.match(tagsPattern);

    if (match) {
      // Nếu tìm thấy tags trong content, loại bỏ chúng
      metadata.content = metadata.content.replace(tagsPattern, '').trim();

      // Nếu chưa có tags riêng biệt, sử dụng tags từ content
      if (!metadata.tags && match[1]) {
        metadata.tags = match[1].trim();
      }

      this.logger.debug(`Admin cleaned up metadata: removed tags from content and moved to tags field`);
    }

    return metadata;
  }

  /**
   * Fetch HTML tối ưu cho admin
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML content
   */
  private async fetchOptimalHtml(url: string, maxRetries = 3): Promise<string> {
    return await this.fetchHeadWithRetry(url, maxRetries);
  }

  /**
   * Trích xuất child URLs từ HTML cho admin
   * @param html HTML content
   * @param baseUrl Base URL
   * @returns Danh sách URLs
   */
  private async extractChildUrlsFromHtml(html: string, baseUrl: string): Promise<string[]> {
    try {
      const $ = cheerio.load(html);
      const baseUrlObj = new URL(baseUrl);
      const urls: string[] = [];

      // Extract from anchor tags
      $('a[href]').each((_, element) => {
        const href = $(element).attr('href');
        if (href) {
          try {
            const fullUrl = new URL(href, baseUrl).href;
            const urlObj = new URL(fullUrl);
            if (urlObj.hostname === baseUrlObj.hostname) {
              urls.push(fullUrl);
            }
          } catch (e) {
            // Ignore invalid URLs
          }
        }
      });

      return [...new Set(urls)];
    } catch (error) {
      this.logger.error(`Admin error extracting URLs from HTML: ${error.message}`);
      return [];
    }
  }

  /**
   * Trích xuất child URLs aggressive cho admin
   * @param html HTML content
   * @param baseUrl Base URL
   * @returns Danh sách URLs
   */
  private async extractChildUrlsAggressive(html: string, baseUrl: string): Promise<string[]> {
    try {
      const $ = cheerio.load(html);
      const baseUrlObj = new URL(baseUrl);
      const urls: string[] = [];

      // Extract from data attributes
      $('[data-href], [data-url], [data-link]').each((_, element) => {
        const dataHref = $(element).attr('data-href') ||
                        $(element).attr('data-url') ||
                        $(element).attr('data-link');
        if (dataHref) {
          try {
            const fullUrl = new URL(dataHref, baseUrl).href;
            const urlObj = new URL(fullUrl);
            if (urlObj.hostname === baseUrlObj.hostname) {
              urls.push(fullUrl);
            }
          } catch (e) {
            // Ignore invalid URLs
          }
        }
      });

      return [...new Set(urls)];
    } catch (error) {
      this.logger.error(`Admin error in aggressive URL extraction: ${error.message}`);
      return [];
    }
  }

  /**
   * Fetch HTML từ URL với retry cho admin
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML content
   */
  private async fetchHeadWithRetry(url: string, maxRetries = 3): Promise<string> {
    try {
      const result = await backOff(
        async () => {
          this.logger.log(`Admin fetching head from URL: ${url}`);

          const response = await firstValueFrom(
            this.httpService.get(url, {
              timeout: 30000,
              maxRedirects: 5,
              responseType: 'text',
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              },
            }),
          );

          const html = response.data;

          // Chỉ trích xuất phần head từ HTML
          const headMatch = html.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
          if (headMatch && headMatch[1]) {
            return `<head>${headMatch[1]}</head>`;
          }

          // Nếu không tìm thấy thẻ head, trả về toàn bộ HTML
          return html;
        },
        {
          numOfAttempts: maxRetries,
          startingDelay: 1000,
          timeMultiple: 2,
          maxDelay: 30000,
          delayFirstAttempt: false,
          jitter: 'full' as const,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Admin attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Admin not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            this.logger.log(
              `Admin retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Admin all retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Chờ theo rate limit nếu cần thiết cho admin
   * @param url URL cần request
   */
  private async waitForRateLimit(url: string): Promise<void> {
    const waitTime = this.checkRateLimit(url);
    if (waitTime > 0) {
      this.logger.log(
        `Admin waiting ${waitTime}ms for rate limit on domain: ${new URL(url).hostname}`,
      );
      await new Promise((resolve) => setTimeout(resolve, waitTime));

      // Kiểm tra lại sau khi chờ
      const remainingWait = this.checkRateLimit(url);
      if (remainingWait > 0) {
        await new Promise((resolve) => setTimeout(resolve, remainingWait));
      }
    }
  }

  /**
   * Kiểm tra và áp dụng rate limiting cho domain cho admin
   * @param url URL cần kiểm tra
   * @returns Số milliseconds cần chờ trước khi có thể request, 0 nếu có thể request ngay
   */
  private checkRateLimit(url: string): number {
    try {
      const domain = new URL(url).hostname;
      const now = Date.now();

      let rateLimit = this.domainRateLimits.get(domain);

      if (!rateLimit) {
        // Lần đầu tiên request domain này
        rateLimit = {
          lastRequest: now,
          requestCount: 1,
          resetTime: now + this.RATE_LIMIT_WINDOW,
        };
        this.domainRateLimits.set(domain, rateLimit);
        return 0;
      }

      // Reset counter nếu đã hết window
      if (now >= rateLimit.resetTime) {
        rateLimit.requestCount = 1;
        rateLimit.lastRequest = now;
        rateLimit.resetTime = now + this.RATE_LIMIT_WINDOW;
        return 0;
      }

      // Kiểm tra số lượng requests trong window
      if (rateLimit.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
        const waitTime = rateLimit.resetTime - now;
        this.logger.warn(
          `Admin rate limit exceeded for domain ${domain}, wait ${waitTime}ms`,
        );
        return waitTime;
      }

      // Adaptive delay
      const timeSinceLastRequest = now - rateLimit.lastRequest;
      if (timeSinceLastRequest < this.MIN_DELAY_BETWEEN_REQUESTS) {
        const waitTime = this.MIN_DELAY_BETWEEN_REQUESTS - timeSinceLastRequest;
        this.logger.debug(
          `Admin delay for domain ${domain}, wait ${waitTime}ms`,
        );
        return waitTime;
      }

      // Cập nhật rate limit
      rateLimit.requestCount++;
      rateLimit.lastRequest = now;

      return 0;
    } catch (error) {
      this.logger.warn(
        `Admin error checking rate limit for ${url}: ${error.message}`,
      );
      return 0; // Cho phép request nếu có lỗi
    }
  }

  /**
   * Chuẩn hóa URL cho admin
   * @param url URL cần chuẩn hóa
   * @returns URL đã chuẩn hóa
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      // Loại bỏ fragment
      urlObj.hash = '';

      // Loại bỏ các query params không cần thiết
      const paramsToRemove = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'fbclid',
        'gclid',
      ];
      const params = new URLSearchParams(urlObj.search);

      paramsToRemove.forEach((param) => {
        if (params.has(param)) {
          params.delete(param);
        }
      });

      urlObj.search = params.toString();

      // Đảm bảo URL kết thúc bằng / nếu không có path
      if (urlObj.pathname === '') {
        urlObj.pathname = '/';
      }

      return urlObj.toString();
    } catch (error) {
      // Nếu không thể chuẩn hóa, trả về URL gốc
      return url;
    }
  }

  /**
   * Cập nhật progress và gọi callback nếu có cho admin
   * @param sessionId ID của session crawl
   * @param progress Thông tin progress hiện tại
   */
  private async updateProgress(sessionId: string, progress: CrawlProgress): Promise<void> {
    const callback = this.progressCallbacks.get(sessionId);
    if (callback) {
      // Tính toán estimated time remaining
      const elapsedTime = Date.now() - progress.startTime;
      if (progress.processedUrls > 0) {
        const avgTimePerUrl = elapsedTime / progress.processedUrls;
        const remainingUrls = progress.totalUrls - progress.processedUrls;
        progress.estimatedTimeRemaining = Math.round(
          (avgTimePerUrl * remainingUrls) / 1000,
        ); // seconds
      }

      try {
        callback(progress);
      } catch (error) {
        this.logger.warn(
          `Admin error in progress callback for session ${sessionId}: ${error.message}`,
        );
      }
    }

    // Cập nhật progress vào database
    const percentage = progress.totalUrls > 0
      ? Math.min(Math.round((progress.processedUrls / progress.totalUrls) * 100), 100)
      : 0;

    await this.updateSessionProgress(sessionId, {
      totalUrls: progress.totalUrls,
      processedUrls: progress.processedUrls,
      successfulUrls: progress.successfulUrls,
      failedUrls: progress.failedUrls,
      currentDepth: progress.currentDepth,
      currentUrl: progress.currentUrl,
      estimatedTimeRemaining: progress.estimatedTimeRemaining,
      percentage,
    });

    // Log progress
    this.logger.log(
      `Admin Progress [${sessionId}]: ${percentage}% (${progress.processedUrls}/${progress.totalUrls}) - Success: ${progress.successfulUrls}, Failed: ${progress.failedUrls}`,
    );
  }

  /**
   * Cập nhật tiến độ session trong database cho admin
   * @param sessionId ID của session
   * @param progress Thông tin tiến độ
   */
  private async updateSessionProgress(
    sessionId: string,
    progress: CrawlSession['progress']
  ): Promise<void> {
    try {
      this.logger.debug(`📊 Cập nhật admin progress vào database cho session ${sessionId}: ${progress.percentage}%`);
      const success = await this.crawlSessionCustomRepository.updateSessionProgress(sessionId, progress);
      if (success) {
        this.logger.debug(`✅ Đã cập nhật admin progress thành công cho session ${sessionId}`);
      } else {
        this.logger.warn(`⚠️ Không thể cập nhật admin progress cho session ${sessionId} - session có thể không tồn tại`);
      }
    } catch (error) {
      this.logger.error(`❌ Lỗi khi cập nhật admin progress cho session ${sessionId}: ${error.message}`);
    }
  }

  /**
   * Cập nhật kết quả session trong database cho admin
   * @param sessionId ID của session
   * @param status Trạng thái mới
   * @param result Kết quả crawl
   */
  private async updateSessionResult(
    sessionId: string,
    status: CrawlSession['status'],
    result: CrawlSession['result']
  ): Promise<void> {
    try {
      const endTime = status !== 'running' ? Date.now() : undefined;
      await this.crawlSessionCustomRepository.updateSessionResult(
        sessionId,
        status,
        result,
        endTime
      );
    } catch (error) {
      this.logger.warn(`Admin lỗi khi cập nhật kết quả cho session ${sessionId}: ${error.message}`);
    }
  }
}
