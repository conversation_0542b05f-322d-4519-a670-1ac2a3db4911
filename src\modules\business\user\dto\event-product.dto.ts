import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsArray,
  ValidateNested,
  IsEnum
} from 'class-validator';
import { TicketTypeDto } from './ticket-type.dto';

/**
 * Enum cho hình thức tổ chức sự kiện
 */
export enum EventFormatEnum {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
  HYBRID = 'HYBRID'
}

/**
 * DTO cho thông tin nâng cao của sự kiện
 */
export class EventProductDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 75,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  purchaseCount: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> thức tổ chức sự kiện',
    enum: EventFormatEnum,
    example: EventFormatEnum.OFFLINE,
  })
  @IsEnum(EventFormatEnum)
  eventFormat: EventFormatEnum;

  @ApiProperty({
    description: 'Đường dẫn tham gia sự kiện (cho sự kiện online)',
    example: 'https://zoom.us/j/123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLink?: string;

  @ApiProperty({
    description: 'Địa điểm tham gia sự kiện (cho sự kiện offline)',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLocation?: string;

  @ApiProperty({
    description: 'Ngày bắt đầu sự kiện (timestamp)',
    example: 1704067200000,
  })
  @IsNumber()
  @Type(() => Number)
  startDate: number;

  @ApiProperty({
    description: 'Ngày kết thúc sự kiện (timestamp)',
    example: 1704153600000,
  })
  @IsNumber()
  @Type(() => Number)
  endDate: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiProperty({
    description: 'Danh sách loại vé sự kiện (Optional)',
    type: [TicketTypeDto],
    example: [
      {
        name: 'Vé thường',
        price: 200000,
        startTime: 1704067200000,
        endTime: 1704153600000,
        timezone: 'Asia/Ho_Chi_Minh',
        description: 'Vé tham gia sự kiện cơ bản',
        quantity: 100,
        minQuantityPerPurchase: 1,
        maxQuantityPerPurchase: 5,
        status: 'PENDING',
        imagesMediaTypes: ['image/jpeg']
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketTypeDto)
  ticketTypes?: TicketTypeDto[];
}
