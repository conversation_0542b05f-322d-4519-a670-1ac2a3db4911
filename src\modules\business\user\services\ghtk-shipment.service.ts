import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GHTKConfigValidationHelper } from '../helpers/ghtk-config-validation.helper';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import {
  GHTK_ENDPOINTS,
  GHTK_DEFAULT_HEADERS,
  GHTK_ERROR_MESSAGES
} from '@modules/business/constants/ghtk.constants';
import {
  IGHTKConfig,
  IGHTKService,
  IGHTKCreateOrderResponse,
  IGHTKCalculateFeeResponse,
  IGHTKOrderStatusResponse,
  IGHTKCancelOrderResponse,
  IGHTKPickupAddressesResponse,
  IGHTKLevel4AddressResponse,
  IGHTKSearchProductResponse,
  IGHTKSolutionsResponse,
} from '@modules/business/interfaces/ghtk.interface';
import {
  CreateGHTKOrderRequestDto,
  CalculateGHTKFeeRequestDto,
  PrintGHTKLabelRequestDto,
  GetGHTKLevel4AddressRequestDto,
  GHTKWebhookDataDto
} from '../dto/ghtk';

/**
 * Service xử lý tất cả các tính năng vận chuyển GHTK
 */
@Injectable()
export class GHTKShipmentService implements IGHTKService {
  private readonly logger = new Logger(GHTKShipmentService.name);
  private config: IGHTKConfig;
  private httpClient: AxiosInstance;

  constructor(
    private readonly configService: ConfigService,
    private readonly configValidationHelper: GHTKConfigValidationHelper
  ) {
    // Validate cấu hình trước khi khởi tạo
    const validation = this.configValidationHelper.validateGHTKConfig();

    if (!validation.isValid) {
      this.logger.error('GHTK configuration validation failed:', validation.errors);
      // Log warnings nhưng vẫn tiếp tục với cấu hình mặc định
      if (validation.warnings.length > 0) {
        this.logger.warn('GHTK configuration warnings:', validation.warnings);
      }
    }

    // Sử dụng cấu hình đã được validate
    this.config = validation.config;

    this.initializeHttpClient();

    this.logger.log('GHTK Service initialized', {
      baseUrl: this.config.baseUrl,
      isTestMode: this.config.isTestMode,
      hasToken: !!this.config.token,
      hasPartnerCode: !!this.config.partnerCode,
      validationPassed: validation.isValid,
      warningsCount: validation.warnings.length
    });

    // Log configuration report in debug mode
    if (this.configService.get<string>('LOG_LEVEL') === 'debug') {
      this.logger.debug('GHTK Configuration Report:\n' + this.configValidationHelper.generateConfigReport());
    }
  }

  /**
   * Khởi tạo HTTP client với cấu hình
   */
  private initializeHttpClient(): void {
    const headers: any = {
      ...GHTK_DEFAULT_HEADERS,
      'Token': this.config.token
    };

    // Chỉ thêm X-Client-Source header nếu có partner code
    if (this.config.partnerCode) {
      headers['X-Client-Source'] = this.config.partnerCode;
    }

    this.httpClient = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers
    });

    // Request interceptor
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`GHTK API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          params: config.params,
          data: config.data
        });
        return config;
      },
      (error) => {
        this.logger.error('GHTK API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`GHTK API Response: ${response.status}`, {
          data: response.data
        });
        return response;
      },
      (error) => {
        this.logger.error('GHTK API Response Error:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Thiết lập cấu hình GHTK
   */
  setConfig(config: IGHTKConfig): void {
    this.config = { ...this.config, ...config };
    this.initializeHttpClient();
    this.logger.log('GHTK configuration updated', {
      baseUrl: this.config.baseUrl,
      isTestMode: this.config.isTestMode
    });
  }

  /**
   * Lấy cấu hình hiện tại
   */
  getConfig(): IGHTKConfig {
    return { ...this.config };
  }

  /**
   * Lấy danh sách giải pháp GHTK
   */
  async getSolutions(): Promise<any> {
    try {
      this.logger.log('Lấy danh sách giải pháp GHTK');

      const response: AxiosResponse<IGHTKSolutionsResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.GET_SOLUTIONS
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      // Transform response to match DTO format
      const transformedData = {
        success: response.data.success,
        message: response.data.message,
        rid: response.data.rid,
        code: response.data.code,
        data: response.data.data?.map(item => ({
          solutionId: item.solution_id,
          description: item.description,
          groupName: item.group_name
        })) || []
      };

      this.logger.log(`Lấy thành công ${transformedData.data.length} giải pháp GHTK`);
      return transformedData;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách giải pháp GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Tạo đơn hàng GHTK
   */
  async createOrder(request: CreateGHTKOrderRequestDto): Promise<any> {
    try {
      this.logger.log('Tạo đơn hàng GHTK', { orderId: request.order.id });

      // Transform DTO to API format
      const apiRequest = {
        products: request.products.map(product => ({
          name: product.name,
          price: product.price,
          weight: product.weight,
          quantity: product.quantity,
          product_code: product.productCode
        })),
        order: {
          id: request.order.id,
          pick_name: request.order.pickName,
          pick_address: request.order.pickAddress,
          pick_province: request.order.pickProvince,
          pick_district: request.order.pickDistrict,
          pick_ward: request.order.pickWard,
          pick_tel: request.order.pickTel,
          name: request.order.name,
          address: request.order.address,
          province: request.order.province,
          district: request.order.district,
          ward: request.order.ward,
          hamlet: request.order.hamlet,
          tel: request.order.tel,
          is_freeship: request.order.isFreeship,
          pick_date: request.order.pickDate,
          pick_money: request.order.pickMoney,
          note: request.order.note,
          value: request.order.value,
          transport: request.order.transport,
          pick_option: request.order.pickOption,
          deliver_option: request.order.deliverOption,
          tags: request.order.tags,
          sub_tags: request.order.subTags
        }
      };

      const response: AxiosResponse<IGHTKCreateOrderResponse> = await this.httpClient.post(
        GHTK_ENDPOINTS.CREATE_ORDER,
        apiRequest
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      // Transform response to match DTO format
      const transformedData = {
        success: response.data.success,
        message: response.data.message,
        rid: response.data.rid,
        code: response.data.code,
        order: {
          partnerId: response.data.order.partner_id,
          label: response.data.order.label,
          area: response.data.order.area,
          fee: response.data.order.fee,
          insuranceFee: response.data.order.insurance_fee,
          trackingId: response.data.order.tracking_id,
          estimatedPickTime: response.data.order.estimated_pick_time,
          estimatedDeliverTime: response.data.order.estimated_deliver_time,
          products: response.data.order.products,
          statusId: response.data.order.status_id
        }
      };

      this.logger.log('Tạo đơn hàng GHTK thành công', {
        orderId: request.order.id,
        label: transformedData.order.label,
        trackingId: transformedData.order.trackingId
      });

      return transformedData;
    } catch (error) {
      this.logger.error('Lỗi khi tạo đơn hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Tính phí vận chuyển GHTK
   */
  async calculateFee(params: CalculateGHTKFeeRequestDto): Promise<any> {
    try {
      this.logger.log('Tính phí vận chuyển GHTK', {
        from: `${params.pickProvince}, ${params.pickDistrict}`,
        to: `${params.province}, ${params.district}`,
        weight: params.weight
      });

      // Chuyển đổi params sang format GHTK API
      const queryParams = {
        pick_address_id: params.pickAddressId,
        pick_address: params.pickAddress,
        pick_province: params.pickProvince,
        pick_district: params.pickDistrict,
        pick_ward: params.pickWard,
        pick_street: params.pickStreet,
        address: params.address,
        province: params.province,
        district: params.district,
        ward: params.ward,
        street: params.street,
        weight: params.weight,
        value: params.value,
        transport: params.transport,
        deliver_option: params.deliverOption,
        tags: params.tags
      };

      const response: AxiosResponse<IGHTKCalculateFeeResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.CALCULATE_FEE,
        { params: queryParams }
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      // Transform response to match DTO format
      const transformedData = {
        success: response.data.success,
        message: response.data.message,
        rid: response.data.rid,
        code: response.data.code,
        fee: {
          name: response.data.fee.name,
          fee: response.data.fee.fee,
          insuranceFee: response.data.fee.insurance_fee,
          deliveryType: response.data.fee.delivery_type,
          a: response.data.fee.a,
          dt: response.data.fee.dt,
          extFees: response.data.fee.extFees?.map(extFee => ({
            display: extFee.display,
            title: extFee.title,
            amount: extFee.amount,
            type: extFee.type
          })) || [],
          delivery: response.data.fee.delivery
        }
      };

      this.logger.log('Tính phí vận chuyển GHTK thành công', {
        fee: transformedData.fee.fee,
        insuranceFee: transformedData.fee.insuranceFee,
        delivery: transformedData.fee.delivery
      });

      return transformedData;
    } catch (error) {
      this.logger.error('Lỗi khi tính phí vận chuyển GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Lấy trạng thái đơn hàng GHTK
   */
  async getOrderStatus(trackingOrder: string): Promise<any> {
    try {
      this.logger.log('Lấy trạng thái đơn hàng GHTK', { trackingOrder });

      const response: AxiosResponse<IGHTKOrderStatusResponse> = await this.httpClient.get(
        `${GHTK_ENDPOINTS.GET_ORDER_STATUS}/${trackingOrder}`
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.ORDER_NOT_FOUND
        );
      }

      // Transform response to match DTO format
      const transformedData = {
        success: response.data.success,
        message: response.data.message,
        rid: response.data.rid,
        code: response.data.code,
        order: {
          labelId: response.data.order.label_id,
          partnerId: response.data.order.partner_id,
          status: response.data.order.status,
          statusText: response.data.order.status_text,
          created: response.data.order.created,
          modified: response.data.order.modified,
          message: response.data.order.message,
          pickDate: response.data.order.pick_date,
          deliverDate: response.data.order.deliver_date,
          customerFullname: response.data.order.customer_fullname,
          customerTel: response.data.order.customer_tel,
          address: response.data.order.address,
          storageDay: response.data.order.storage_day,
          shipMoney: response.data.order.ship_money,
          insurance: response.data.order.insurance,
          value: response.data.order.value,
          weight: response.data.order.weight,
          pickMoney: response.data.order.pick_money,
          isFreeship: response.data.order.is_freeship,
          statusId: response.data.order.status_id
        }
      };

      this.logger.log('Lấy trạng thái đơn hàng GHTK thành công', {
        trackingOrder,
        status: transformedData.order.statusText,
        statusId: transformedData.order.statusId
      });

      return transformedData;
    } catch (error) {
      this.logger.error('Lỗi khi lấy trạng thái đơn hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Hủy đơn hàng GHTK
   */
  async cancelOrder(trackingOrder: string): Promise<any> {
    try {
      this.logger.log('Hủy đơn hàng GHTK', { trackingOrder });

      const response: AxiosResponse<IGHTKCancelOrderResponse> = await this.httpClient.post(
        `${GHTK_ENDPOINTS.CANCEL_ORDER}/${trackingOrder}`
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.CANNOT_CANCEL
        );
      }

      // Transform response to match DTO format
      const transformedData = {
        success: response.data.success,
        message: response.data.message,
        rid: response.data.rid,
        code: response.data.code,
        logId: response.data.log_id || 'N/A'
      };

      this.logger.log('Hủy đơn hàng GHTK thành công', {
        trackingOrder,
        logId: transformedData.logId
      });

      return transformedData;
    } catch (error) {
      this.logger.error('Lỗi khi hủy đơn hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * In nhãn đơn hàng GHTK
   */
  async printLabel(trackingOrder: string, options?: PrintGHTKLabelRequestDto): Promise<Buffer> {
    try {
      this.logger.log('In nhãn đơn hàng GHTK', { trackingOrder, options });

      const queryParams: any = {};
      if (options?.original) queryParams.original = options.original;
      if (options?.paperSize) queryParams.paper_size = options.paperSize;

      const response: AxiosResponse<Buffer> = await this.httpClient.get(
        `${GHTK_ENDPOINTS.PRINT_LABEL}/${trackingOrder}`,
        {
          params: queryParams,
          responseType: 'arraybuffer'
        }
      );

      this.logger.log('In nhãn đơn hàng GHTK thành công', {
        trackingOrder,
        size: response.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi in nhãn đơn hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Lấy danh sách địa chỉ lấy hàng GHTK
   */
  async getPickupAddresses(): Promise<any> {
    try {
      this.logger.log('Lấy danh sách địa chỉ lấy hàng GHTK');

      const response: AxiosResponse<IGHTKPickupAddressesResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.GET_PICKUP_ADDRESSES
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      // Transform response to match DTO format
      const transformedData = {
        success: response.data.success,
        message: response.data.message,
        rid: response.data.rid,
        code: response.data.code,
        data: response.data.data?.map(item => ({
          pickAddressId: item.pick_address_id,
          address: item.address,
          pickTel: item.pick_tel,
          pickName: item.pick_name
        })) || []
      };

      this.logger.log(`Lấy thành công ${transformedData.data.length} địa chỉ lấy hàng GHTK`);
      return transformedData;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách địa chỉ lấy hàng GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Lấy danh sách địa chỉ cấp 4 GHTK
   */
  async getLevel4Address(params: GetGHTKLevel4AddressRequestDto): Promise<any> {
    try {
      this.logger.log('Lấy danh sách địa chỉ cấp 4 GHTK', params);

      const queryParams = {
        province: params.province,
        district: params.district,
        ward_street: params.wardStreet,
        address: params.address
      };

      const response: AxiosResponse<IGHTKLevel4AddressResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.GET_LEVEL4_ADDRESS,
        { params: queryParams }
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      // Transform response to match DTO format (data is already string array)
      const transformedData = {
        success: response.data.success,
        message: response.data.message,
        rid: response.data.rid,
        code: response.data.code,
        data: response.data.data || []
      };

      this.logger.log(`Lấy thành công ${transformedData.data.length} địa chỉ cấp 4 GHTK`);
      return transformedData;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách địa chỉ cấp 4 GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Tìm kiếm sản phẩm GHTK
   */
  async searchProducts(term: string): Promise<any> {
    try {
      this.logger.log('Tìm kiếm sản phẩm GHTK', { term });

      const response: AxiosResponse<IGHTKSearchProductResponse> = await this.httpClient.get(
        GHTK_ENDPOINTS.SEARCH_PRODUCTS,
        { params: { term } }
      );

      if (!response.data.success) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_API_ERROR,
          response.data.message || GHTK_ERROR_MESSAGES.API_ERROR
        );
      }

      // Transform response to match DTO format
      const transformedData = {
        success: response.data.success,
        message: response.data.message,
        rid: response.data.rid,
        code: response.data.code,
        data: response.data.data?.map(item => ({
          fullName: item.full_name,
          productCode: item.product_code,
          weigh: item.weigh,
          cost: item.cost
        })) || []
      };

      this.logger.log(`Tìm thấy ${transformedData.data.length} sản phẩm GHTK`);
      return transformedData;
    } catch (error) {
      this.logger.error('Lỗi khi tìm kiếm sản phẩm GHTK:', error);
      this.handleGHTKError(error);
    }
  }

  /**
   * Xử lý webhook từ GHTK
   */
  async handleWebhook(data: GHTKWebhookDataDto): Promise<void> {
    try {
      this.logger.log('Xử lý webhook từ GHTK', {
        partnerId: data.partnerId,
        labelId: data.labelId,
        statusId: data.statusId
      });

      // Transform DTO to internal format if needed
      const webhookData = {
        partner_id: data.partnerId,
        label_id: data.labelId,
        status_id: data.statusId,
        action_time: data.actionTime,
        reason_code: data.reasonCode,
        reason: data.reason,
        weight: data.weight,
        fee: data.fee,
        pick_money: data.pickMoney,
        return_part_package: data.returnPartPackage
      };

      // TODO: Implement webhook handling logic
      // - Cập nhật trạng thái đơn hàng trong database
      // - Gửi thông báo cho user
      // - Log lịch sử thay đổi trạng thái

      this.logger.log('Xử lý webhook GHTK thành công');
    } catch (error) {
      this.logger.error('Lỗi khi xử lý webhook GHTK:', error);
      throw error;
    }
  }

  /**
   * Xử lý lỗi từ GHTK API
   */
  private handleGHTKError(error: any): never {
    if (error instanceof AppException) {
      throw error;
    }

    if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_TIMEOUT_ERROR,
        GHTK_ERROR_MESSAGES.TIMEOUT_ERROR
      );
    }

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_NETWORK_ERROR,
        GHTK_ERROR_MESSAGES.NETWORK_ERROR
      );
    }

    if (error.response?.status === 401) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_INVALID_TOKEN,
        `${GHTK_ERROR_MESSAGES.INVALID_TOKEN}. Vui lòng kiểm tra GHTK_TOKEN trong environment variables.`
      );
    }

    // Xử lý lỗi token từ GHTK API response
    if (error.response?.data?.code === 10016) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_INVALID_TOKEN,
        'Token GHTK không hợp lệ hoặc đã hết hạn. Vui lòng cập nhật GHTK_TOKEN với token thực tế từ tài khoản GHTK của bạn.'
      );
    }

    if (error.response?.status === 404) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_ORDER_NOT_FOUND,
        GHTK_ERROR_MESSAGES.ORDER_NOT_FOUND
      );
    }

    // Lỗi chung
    const message = error.response?.data?.message || error.message || GHTK_ERROR_MESSAGES.API_ERROR;
    throw new AppException(
      BUSINESS_ERROR_CODES.GHTK_API_ERROR,
      message
    );
  }
}
