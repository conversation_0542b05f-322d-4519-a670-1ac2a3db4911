import { PaginatedResult } from '@/common/response';
import { TypeAgentStatus } from '@modules/agent/constants';
import { TypeAgent } from '@modules/agent/entities';
import { TypeAgentQueryDto } from '@modules/agent/user/dto';
import { AdminTool } from '@modules/tools/entities';

import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';

/**
 * Interface cho tool data từ type agent (đơn giản)
 */
export interface TypeAgentToolData {
  id: string;
  name: string;
  description: string | null;
  versionName: string | null;
}

/**
 * Repository cho TypeAgent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến loại agent
 */
@Injectable()
export class TypeAgentRepository extends Repository<TypeAgent> {
  private readonly logger = new Logger(TypeAgentRepository.name);

  constructor(private dataSource: DataSource) {
    super(TypeAgent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho TypeAgent
   * @returns SelectQueryBuilder cho TypeAgent
   */
  private createBaseQuery(): SelectQueryBuilder<TypeAgent> {
    return this.createQueryBuilder('typeAgent');
  }

  /**
   * Tìm loại agent theo ID
   * @param id ID của loại agent
   * @returns TypeAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: number, includeDeleted: boolean = false): Promise<TypeAgent | null> {
    const query = this.createBaseQuery()
      .where('typeAgent.id = :id', { id });

    // Nếu không bao gồm các bản ghi đã bị xóa mềm
    if (!includeDeleted) {
      query.andWhere('typeAgent.deletedAt IS NULL');
    }

    return query.getOne();
  }

  /**
   * Tìm loại agent theo tên
   * @param name Tên của loại agent
   * @returns TypeAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findByName(name: string, includeDeleted: boolean = false): Promise<TypeAgent | null> {
    const query = this.createBaseQuery()
      .where('typeAgent.name = :name', { name });

    // Nếu không bao gồm các bản ghi đã bị xóa mềm
    if (!includeDeleted) {
      query.andWhere('typeAgent.deletedAt IS NULL');
    }

    return query.getOne();
  }

  /**
   * Lấy danh sách loại agent với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái loại agent (tùy chọn)
   * @param userId ID của người dùng (tùy chọn, nếu cần lọc theo người dùng)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách loại agent với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    status?: TypeAgentStatus,
    userId?: number,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<TypeAgent>> {
    const qb = this.createBaseQuery();

    // Chỉ lấy các bản ghi chưa bị xóa mềm
    qb.andWhere('typeAgent.deletedAt IS NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(typeAgent.name ILIKE :search OR typeAgent.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Đối với user, chỉ hiển thị các loại agent có status là APPROVED (tất cả đều do admin tạo)
    if (userId) {
      qb.andWhere('typeAgent.status = :approvedStatus', {
        approvedStatus: TypeAgentStatus.APPROVED
      });
    } else if (status) {
      // Nếu không phải user view (admin view) và có truyền status, thì lọc theo status
      qb.andWhere('typeAgent.status = :status', { status });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`typeAgent.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Lấy danh sách tất cả loại agent
   * @returns Danh sách loại agent
   */
  async findAll(): Promise<TypeAgent[]> {
    return this.createBaseQuery().getMany();
  }

  /**
   * Xóa mềm tùy chỉnh cho TypeAgent
   * @param id ID của loại agent cần xóa mềm
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns Kết quả xóa mềm
   */
  async customSoftDelete(id: number, employeeId: number): Promise<boolean> {
    try {
      // Cập nhật trường deletedAt và deletedBy
      const result = await this.createQueryBuilder()
        .update(TypeAgent)
        .set({
          deletedAt: Date.now(),
          deletedBy: employeeId
        })
        .where('id = :id', { id })
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Error in customSoftDelete: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách loại agent với phân trang theo query DTO (tối ưu với tool count)
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách loại agent với phân trang và tool count
   */
  async findPaginatedByQuery(
    queryDto: TypeAgentQueryDto,
    userId: number,
  ): Promise<PaginatedResult<TypeAgent & { toolCount: number }>> {
    try {
      // Tạo query với LEFT JOIN để lấy user tool count trong 1 query duy nhất
      const qb = this.createQueryBuilder('typeAgent')
        .leftJoin('user_type_agent_tools', 'utat', 'utat.type_id = typeAgent.id')
        .leftJoin('user_tools', 'ut', 'utat.tool_id = ut.id AND ut.user_id = :userId', { userId })
        .select([
          'typeAgent.id',
          'typeAgent.name',
          'typeAgent.description',
          'typeAgent.config',
          'typeAgent.createdAt',
          'typeAgent.status'
        ])
        .addSelect('COUNT(ut.id)', 'toolCount')
        .where('typeAgent.deletedAt IS NULL')
        .andWhere('typeAgent.status = :approvedStatus', {
          approvedStatus: TypeAgentStatus.APPROVED
        })
        .groupBy('typeAgent.id');

      // Thêm điều kiện tìm kiếm nếu có
      if (queryDto.search) {
        qb.andWhere('(typeAgent.name ILIKE :search OR typeAgent.description ILIKE :search)',
          { search: `%${queryDto.search}%` });
      }

      // Thêm sắp xếp và phân trang
      qb.orderBy(`typeAgent.${queryDto.sortBy}`, queryDto.sortDirection)
        .skip((queryDto.page - 1) * queryDto.limit)
        .take(queryDto.limit);

      // Lấy dữ liệu với tool count trong 1 query duy nhất
      const rawResults = await qb.getRawMany();

      // Chuyển đổi raw results thành TypeAgent entities với toolCount
      const items = rawResults.map(raw => {
        const typeAgent = new TypeAgent();
        typeAgent.id = raw.typeAgent_id;
        typeAgent.name = raw.typeAgent_name;
        typeAgent.description = raw.typeAgent_description;
        typeAgent.config = raw.typeAgent_config;
        typeAgent.createdAt = raw.typeAgent_createdAt;
        typeAgent.updatedAt = raw.typeAgent_updatedAt;
        typeAgent.status = raw.typeAgent_status;

        // Thêm toolCount
        (typeAgent as any).toolCount = parseInt(raw.toolCount || '0', 10);

        return typeAgent as TypeAgent & { toolCount: number };
      });

      // Tính toán meta pagination đơn giản (không cần query riêng để đếm total)
      const hasNextPage = items.length === queryDto.limit;
      const estimatedTotal = hasNextPage
        ? (queryDto.page * queryDto.limit) + 1
        : items.length + ((queryDto.page - 1) * queryDto.limit);

      return {
        items,
        meta: {
          totalItems: estimatedTotal,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: hasNextPage ? queryDto.page + 1 : queryDto.page,
          currentPage: queryDto.page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi truy vấn danh sách loại agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Đếm số lượng user tools của type agent cho user cụ thể
   * @param typeAgentId ID của type agent
   * @param userId ID của user
   * @returns Số lượng user tools
   */
  async countToolsByTypeAgentId(typeAgentId: number, userId: number): Promise<number> {
    try {
      const result = await this.dataSource
        .createQueryBuilder()
        .select('COUNT(*)', 'count')
        .from('user_type_agent_tools', 'utat')
        .innerJoin('user_tools', 'ut', 'utat.tool_id = ut.id')
        .where('utat.type_id = :typeAgentId', { typeAgentId })
        .andWhere('ut.user_id = :userId', { userId })
        .andWhere('ut.deleted_at IS NULL') // Chỉ đếm tools chưa bị xóa
        .getRawOne();

      return parseInt(result?.count || '0', 10);
    } catch (error) {
      this.logger.error(`Lỗi khi đếm user tools của type agent ${typeAgentId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Lấy danh sách user tools của type agent cho user cụ thể
   * @param typeAgentId ID của type agent
   * @param userId ID của user
   * @returns Danh sách user tools
   */
  async getToolsByTypeAgentId(typeAgentId: number, userId: number): Promise<TypeAgentToolData[]> {
    try {
      const tools = await this.dataSource
        .createQueryBuilder()
        .select([
          'ut.id',
          'ut.name',
          'ut.description',
          'utv.version_name'
        ])
        .from('user_type_agent_tools', 'utat')
        .innerJoin('user_tools', 'ut', 'utat.tool_id = ut.id')
        .leftJoin('user_tool_versions', 'utv', 'utv.original_tool_id = ut.id AND utv.isDefault = true')
        .where('utat.type_id = :typeAgentId', { typeAgentId })
        .andWhere('ut.user_id = :userId', { userId })
        .orderBy('ut.name', 'ASC') // Sắp xếp theo tên tool
        .getRawMany();

      return tools.map(tool => ({
        id: tool.ut_id,
        name: tool.ut_name,
        description: tool.ut_description,
        versionName: tool.utv_version_name
      })) as TypeAgentToolData[];
    } catch (error) {
      this.logger.error(`Lỗi khi lấy tools của type agent ${typeAgentId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Validate admin tools có tồn tại không
   * @param toolIds Danh sách ID của admin tools
   * @returns Object chứa thông tin validation
   */
  async validateAdminTools(toolIds: string[]): Promise<{
    valid: boolean;
    existingIds: string[];
    nonExistingIds: string[];
  }> {
    try {
      if (!toolIds || toolIds.length === 0) {
        return {
          valid: true,
          existingIds: [],
          nonExistingIds: [],
        };
      }

      // Kiểm tra tools có tồn tại trong admin_tools và chưa bị xóa
      const existingTools = await this.dataSource
        .createQueryBuilder()
        .select('at.id')
        .from(AdminTool, 'at')
        .where('at.id IN (:...toolIds)', { toolIds })
        .andWhere('at.deleted_at IS NULL')
        .getRawMany();

      const existingIds = existingTools.map(tool => tool.id);
      const nonExistingIds = toolIds.filter(id => !existingIds.includes(id));

      return {
        valid: nonExistingIds.length === 0,
        existingIds,
        nonExistingIds,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi validate admin tools: ${error.message}`);
      return {
        valid: false,
        existingIds: [],
        nonExistingIds: toolIds,
      };
    }
  }

  // /**
  //  * Liên kết type agent với admin tools
  //  * @param typeAgentId ID của type agent
  //  * @param toolIds Danh sách ID của admin tools
  //  */
  // async linkAdminTools(typeAgentId: number, toolIds: string[]): Promise<void> {
  //   try {
  //     // Xóa tất cả liên kết cũ
  //     await this.dataSource
  //       .createQueryBuilder()
  //       .delete()
  //       .from(AdminTypeAgentTools)
  //       .where('type_id = :typeAgentId', { typeAgentId })
  //       .execute();

  //     // Thêm liên kết mới
  //     if (toolIds && toolIds.length > 0) {
  //       const linkData = toolIds.map(toolId => ({
  //         typeId: typeAgentId,
  //         toolId: toolId,
  //       }));

  //       await this.dataSource
  //         .createQueryBuilder()
  //         .insert()
  //         .into(AdminTypeAgentTools)
  //         .values(linkData)
  //         .execute();
  //     }
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi liên kết admin tools: ${error.message}`);
  //     throw error;
  //   }
  // }

  /**
   * Đếm số lượng admin tools của type agent
   * @param typeAgentId ID của type agent
   * @returns Số lượng tools
   */
  // async countAdminToolsByTypeAgentId(typeAgentId: number): Promise<number> {
  //   try {
  //     const result = await this.dataSource
  //       .createQueryBuilder()
  //       .select('COUNT(*)', 'count')
  //       .from(AdminTypeAgentTools, 'atat')
  //       .innerJoin(AdminTool, 'at', 'atat.tool_id = at.id')
  //       .where('atat.type_id = :typeAgentId', { typeAgentId })
  //       .andWhere('at.deleted_at IS NULL')
  //       .getRawOne();

  //     return parseInt(result?.count || '0', 10);
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi đếm admin tools của type agent ${typeAgentId}: ${error.message}`);
  //     return 0;
  //   }
  // }

  /**
   * Lấy danh sách admin tools của type agent với thông tin chi tiết
   * @param typeAgentId ID của type agent
   * @returns Danh sách admin tools với thông tin chi tiết
   */
  // async getAdminToolsByTypeAgentId(typeAgentId: number): Promise<{
  //   id: string;
  //   name: string;
  //   description: string | null;
  //   versionName: string | null;
  //   versionId: string | null;
  // }[]> {
  //   try {
  //     const tools = await this.dataSource
  //       .createQueryBuilder()
  //       .select([
  //         'at.id',
  //         'at.name',
  //         'at.description',
  //         'atv.id as version_id',
  //         'atv.version_name'
  //       ])
  //       .from(AdminTypeAgentTools, 'atat')
  //       .innerJoin(AdminTool, 'at', 'atat.tool_id = at.id')
  //       .leftJoin('admin_tool_versions', 'atv', 'at.version_default = atv.id')
  //       .where('atat.type_id = :typeAgentId', { typeAgentId })
  //       .andWhere('at.deleted_at IS NULL')
  //       .orderBy('at.name', 'ASC')
  //       .getRawMany();

  //     return tools.map(tool => ({
  //       id: tool.at_id,
  //       name: tool.at_name,
  //       description: tool.at_description,
  //       versionName: tool.version_name,
  //       versionId: tool.version_id,
  //     }));
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi lấy admin tools của type agent ${typeAgentId}: ${error.message}`);
  //     return [];
  //   }
  // }

  /**
   * Lấy danh sách type agents đã bị xóa mềm với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách type agents đã xóa với phân trang
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<TypeAgent>> {
    const qb = this.createBaseQuery();

    // Chỉ lấy các bản ghi đã bị xóa mềm
    qb.andWhere('typeAgent.deletedAt IS NOT NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(typeAgent.name ILIKE :search OR typeAgent.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`typeAgent.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Khôi phục type agent đã bị xóa mềm
   * @param id ID của type agent cần khôi phục
   * @returns Kết quả khôi phục
   */
  async restoreTypeAgent(id: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .update(TypeAgent)
        .set({
          deletedAt: () => 'NULL',
          deletedBy: () => 'NULL'
        })
        .where('id = :id', { id })
        .andWhere('deletedAt IS NOT NULL') // Chỉ khôi phục những gì đã bị xóa
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục type agent ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Migrate agents từ type agent cũ sang type agent mới
   * @param oldTypeAgentId ID của type agent cũ
   * @param newTypeAgentId ID của type agent mới
   * @returns Số lượng agents đã được migrate
   */
  async migrateAgents(oldTypeAgentId: number, newTypeAgentId: number): Promise<number> {
    try {
      // Cập nhật tất cả agents đang sử dụng oldTypeAgentId
      const result = await this.dataSource
        .createQueryBuilder()
        .update('agents')
        .set({ type_agent_id: newTypeAgentId })
        .where('type_agent_id = :oldTypeAgentId', { oldTypeAgentId })
        .execute();

      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi migrate agents từ ${oldTypeAgentId} sang ${newTypeAgentId}: ${error.message}`);
      throw error;
    }
  }
}
