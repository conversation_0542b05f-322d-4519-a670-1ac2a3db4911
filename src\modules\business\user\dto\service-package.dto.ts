import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsArray,
  MaxLength,
  IsEnum
} from 'class-validator';
import { EntityStatusEnum } from '@modules/business/enums';

/**
 * DTO cho gói dịch vụ
 */
export class ServicePackageDto {
  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Gói tư vấn cơ bản',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Giá gói dịch vụ',
    example: 1000000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  price: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu cung cấp dịch vụ (timestamp)',
    example: 1704067200000,
  })
  @IsNumber()
  @Type(() => Number)
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc cung cấp dịch vụ (timestamp)',
    example: 1704153600000,
  })
  @IsNumber()
  @Type(() => Number)
  endTime: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiProperty({
    description: 'Mô tả gói dịch vụ',
    example: 'Gói tư vấn cơ bản bao gồm 3 buổi tư vấn online',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Số lượng gói dịch vụ có sẵn',
    example: 50,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  quantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu trên 1 lần mua',
    example: 1,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Giới hạn số lượng tối đa trên 1 lần mua',
    example: 3,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  maxQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Trạng thái gói dịch vụ',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.PENDING,
  })
  @IsEnum(EntityStatusEnum)
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Danh sách loại media cho hình ảnh gói dịch vụ',
    type: [String],
    example: ['image/jpeg', 'image/png'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes?: string[];
}
