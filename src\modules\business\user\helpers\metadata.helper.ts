import { Injectable, Logger } from '@nestjs/common';
import { CustomField } from '@modules/business/entities';
import { CustomFieldInputDto, MetadataCustomFieldDto } from '../dto/custom-field-metadata.dto';

/**
 * Helper để xử lý metadata cho custom fields
 */
@Injectable()
export class MetadataHelper {
  private readonly logger = new Logger(MetadataHelper.name);

  /**
   * Chuyển đổi CustomFieldInputDto thành MetadataCustomFieldDto
   * @param customFieldInputs Danh sách input từ user
   * @param customFields Danh sách custom fields từ database
   * @returns Danh sách metadata custom fields
   */
  buildMetadataCustomFields(
    customFieldInputs: CustomFieldInputDto[],
    customFields: CustomField[]
  ): MetadataCustomFieldDto[] {
    if (!customFieldInputs || customFieldInputs.length === 0) {
      return [];
    }

    this.logger.log(`Xây dựng metadata cho ${customFieldInputs.length} custom fields`);

    const metadataFields: MetadataCustomFieldDto[] = [];

    for (const input of customFieldInputs) {
      // Tìm custom field definition từ database
      const customField = customFields.find(cf => cf.id === input.customFieldId);
      
      if (!customField) {
        this.logger.warn(`Không tìm thấy custom field với ID: ${input.customFieldId}`);
        continue;
      }

      // Tạo metadata custom field
      const metadataField: MetadataCustomFieldDto = {
        id: customField.id,
        configId: customField.configId,
        label: customField.label,
        type: customField.type,
        required: customField.required,
        configJson: customField.configJson,
        tags: customField.tags,
        value: input.value
      };

      metadataFields.push(metadataField);
    }

    this.logger.log(`Đã xây dựng metadata cho ${metadataFields.length} custom fields`);
    return metadataFields;
  }

  /**
   * Tạo metadata object cho entity
   * @param customFieldInputs Danh sách custom field inputs
   * @param customFields Danh sách custom fields từ database
   * @param additionalData Dữ liệu bổ sung khác
   * @returns Metadata object
   */
  buildMetadata(
    customFieldInputs: CustomFieldInputDto[] = [],
    customFields: CustomField[] = [],
    additionalData: any = {}
  ): any {
    const metadataCustomFields = this.buildMetadataCustomFields(customFieldInputs, customFields);

    return {
      customFields: metadataCustomFields, // Đổi từ custom_fields thành customFields để phù hợp với database schema
      ...additionalData
    };
  }

  /**
   * Lấy danh sách ID của custom fields từ inputs
   * @param customFieldInputs Danh sách custom field inputs
   * @returns Danh sách ID
   */
  extractCustomFieldIds(customFieldInputs: CustomFieldInputDto[]): number[] {
    if (!customFieldInputs || customFieldInputs.length === 0) {
      return [];
    }

    return customFieldInputs.map(input => input.customFieldId);
  }

  /**
   * Validate custom field inputs
   * @param customFieldInputs Danh sách custom field inputs
   * @param availableCustomFields Danh sách custom fields có sẵn
   * @throws Error nếu có custom field không hợp lệ
   */
  validateCustomFieldInputs(
    customFieldInputs: CustomFieldInputDto[],
    availableCustomFields: CustomField[]
  ): void {
    if (!customFieldInputs || customFieldInputs.length === 0) {
      return;
    }

    const availableIds = availableCustomFields.map(cf => cf.id);

    for (const input of customFieldInputs) {
      if (!availableIds.includes(input.customFieldId)) {
        throw new Error(`Custom field với ID ${input.customFieldId} không tồn tại hoặc không khả dụng`);
      }

      // Validate required fields
      const customField = availableCustomFields.find(cf => cf.id === input.customFieldId);
      if (customField?.required && (!input.value || !input.value.value)) {
        throw new Error(`Custom field "${customField.label}" là bắt buộc`);
      }
    }
  }

  /**
   * Merge metadata mới với metadata hiện tại
   * @param currentMetadata Metadata hiện tại
   * @param newCustomFieldInputs Custom field inputs mới
   * @param customFields Danh sách custom fields từ database
   * @returns Metadata đã được merge
   */
  mergeMetadata(
    currentMetadata: any,
    newCustomFieldInputs: CustomFieldInputDto[],
    customFields: CustomField[]
  ): any {
    const currentCustomFields = currentMetadata?.customFields || []; // Đổi từ custom_fields thành customFields
    const newMetadataFields = this.buildMetadataCustomFields(newCustomFieldInputs, customFields);

    // Merge: giữ lại custom fields cũ không có trong input mới, thêm/cập nhật custom fields mới
    const mergedCustomFields = [...currentCustomFields];

    for (const newField of newMetadataFields) {
      const existingIndex = mergedCustomFields.findIndex(cf => cf.id === newField.id);
      
      if (existingIndex >= 0) {
        // Cập nhật custom field hiện có
        mergedCustomFields[existingIndex] = newField;
      } else {
        // Thêm custom field mới
        mergedCustomFields.push(newField);
      }
    }

    return {
      ...currentMetadata,
      customFields: mergedCustomFields // Đổi từ custom_fields thành customFields
    };
  }

  /**
   * Xây dựng metadata từ classification custom fields (sử dụng customFieldId)
   * @param classificationCustomFields Danh sách classification custom fields với customFieldId
   * @param imagesMediaTypes Danh sách MIME types của ảnh phân loại (chỉ dùng để tạo upload URLs)
   * @param images Danh sách ảnh với key và position (size là optional)
   * @param additionalData Dữ liệu bổ sung khác
   * @returns Metadata object
   */
  buildClassificationMetadata(
    classificationCustomFields: CustomFieldInputDto[] = [],
    imagesMediaTypes: string[] = [],
    images: Array<{key: string, size?: number, position?: number}> = [],
    additionalData: any = {}
  ): any {
    const metadataCustomFields = classificationCustomFields.map(field => ({
      customFieldId: field.customFieldId,
      value: field.value
    }));

    const metadata: any = {
      customFields: metadataCustomFields,
      ...additionalData
    };

    // Lưu thông tin ảnh (key và position, size là optional)
    if (images && images.length > 0) {
      metadata.images = images;
    }

    // Không lưu imagesMediaTypes vào metadata nữa

    return metadata;
  }
}
