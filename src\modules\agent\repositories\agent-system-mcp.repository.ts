import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentSystemMcp } from '@modules/agent/entities';

/**
 * Repository cho AgentSystemMcp
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến ánh xạ agent system và MCP
 */
@Injectable()
export class AgentSystemMcpRepository extends Repository<AgentSystemMcp> {
  private readonly logger = new Logger(AgentSystemMcpRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentSystemMcp, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentSystemMcp
   * @returns SelectQueryBuilder cho AgentSystemMcp
   */
  private createBaseQuery(): SelectQueryBuilder<AgentSystemMcp> {
    return this.createQueryBuilder('agentSystemMcp');
  }

  /**
   * Tìm ánh xạ theo agent ID và MCP ID
   * @param agentId ID của agent system
   * @param mcpId ID của MCP system
   * @returns AgentSystemMcp nếu tìm thấy, null nếu không tìm thấy
   */
  async findByAgentAndMcp(agentId: string, mcpId: string): Promise<AgentSystemMcp | null> {
    return this.createBaseQuery()
      .where('agentSystemMcp.agentId = :agentId', { agentId })
      .andWhere('agentSystemMcp.mcpId = :mcpId', { mcpId })
      .getOne();
  }

  /**
   * Lấy danh sách MCP systems của một agent system
   * @param agentId ID của agent system
   * @returns Danh sách MCP systems
   */
  async findMcpsByAgentId(agentId: string): Promise<any[]> {
    return this.dataSource
      .createQueryBuilder()
      .select([
        'mcp.id',
        'mcp.nameServer',
        'mcp.description',
        'mcp.config'
      ])
      .from('agent_system_mcp', 'asm')
      .innerJoin('mcp_systems', 'mcp', 'asm.mcp_id = mcp.id')
      .where('asm.agent_id = :agentId', { agentId })
      .orderBy('mcp.nameServer', 'ASC')
      .getRawMany();
  }

  /**
   * Lấy danh sách agent systems sử dụng một MCP system
   * @param mcpId ID của MCP system
   * @returns Danh sách agent systems
   */
  async findAgentsByMcpId(mcpId: string): Promise<any[]> {
    return this.dataSource
      .createQueryBuilder()
      .select([
        'ags.id',
        'ags.nameCode',
        'ags.description',
        'ags.active'
      ])
      .from('agent_system_mcp', 'asm')
      .innerJoin('agents_system', 'ags', 'asm.agent_id = ags.id')
      .where('asm.mcp_id = :mcpId', { mcpId })
      .andWhere('ags.deleted_by IS NULL')
      .orderBy('ags.nameCode', 'ASC')
      .getRawMany();
  }

  /**
   * Liên kết agent system với MCP systems
   * @param agentId ID của agent system
   * @param mcpIds Danh sách ID của MCP systems
   */
  async linkAgentWithMcps(agentId: string, mcpIds: string[]): Promise<void> {
    try {
      // Xóa tất cả liên kết cũ
      await this.createQueryBuilder()
        .delete()
        .from(AgentSystemMcp)
        .where('agentId = :agentId', { agentId })
        .execute();

      // Thêm liên kết mới
      if (mcpIds && mcpIds.length > 0) {
        const linkData = mcpIds.map(mcpId => ({
          agentId: agentId,
          mcpId: mcpId,
        }));

        await this.createQueryBuilder()
          .insert()
          .into(AgentSystemMcp)
          .values(linkData)
          .execute();
      }
    } catch (error) {
      this.logger.error(`Lỗi khi liên kết agent system với MCP systems: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa liên kết giữa agent system và MCP system
   * @param agentId ID của agent system
   * @param mcpId ID của MCP system
   * @returns true nếu xóa thành công
   */
  async unlinkAgentFromMcp(agentId: string, mcpId: string): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(AgentSystemMcp)
        .where('agentId = :agentId', { agentId })
        .andWhere('mcpId = :mcpId', { mcpId })
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa liên kết agent-MCP: ${error.message}`);
      return false;
    }
  }

  /**
   * Xóa tất cả liên kết của một agent system
   * @param agentId ID của agent system
   * @returns true nếu xóa thành công
   */
  async unlinkAllMcpsFromAgent(agentId: string): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(AgentSystemMcp)
        .where('agentId = :agentId', { agentId })
        .execute();

      return true; // Luôn trả về true vì có thể không có liên kết nào
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tất cả liên kết MCP của agent ${agentId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Đếm số lượng MCP systems của một agent system
   * @param agentId ID của agent system
   * @returns Số lượng MCP systems
   */
  async countMcpsByAgentId(agentId: string): Promise<number> {
    try {
      const result = await this.createBaseQuery()
        .where('agentSystemMcp.agentId = :agentId', { agentId })
        .getCount();

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm MCP systems của agent ${agentId}: ${error.message}`);
      return 0;
    }
  }
}
