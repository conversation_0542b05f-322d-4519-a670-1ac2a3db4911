import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { UserCampaignRepository } from '../repositories/user-campaign.repository';
import { UserCampaignHistoryRepository } from '../repositories/user-campaign-history.repository';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import { Transactional } from 'typeorm-transactional';
import { In, Like, FindManyOptions } from 'typeorm';
import { CreateCampaignDto, UpdateCampaignDto, CampaignResponseDto, CampaignStatsDto, CampaignStatus, CampaignHistoryResponseDto, SendStatus, CampaignQueryDto, CreateTemplateCampaignDto, CreateTemplateCampaignResponseDto } from '../dto/campaign';
import { UserCampaign, UserCampaignHistory, UserAudience } from '../entities';
import { UserSegmentService } from './user-segment.service';
import { ZaloService } from './zalo.service';
import { AppException, ErrorCode } from '@/common';
import { UserTemplateEmailRepository } from '../repositories/user-template-email.repository';
import { EmailServerConfigurationUserService } from '@/modules/integration/user/services/email-server-configuration-user.service';
import { PaginatedResult } from '@/common/response';

/**
 * Service xử lý logic liên quan đến campaign
 */
@Injectable()
export class UserCampaignService {
  constructor(
    private readonly userCampaignRepository: UserCampaignRepository,
    private readonly userCampaignHistoryRepository: UserCampaignHistoryRepository,
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userSegmentRepository: UserSegmentRepository,
    private readonly userSegmentService: UserSegmentService,
    private readonly zaloService: ZaloService,
    private readonly userTemplateEmailRepository: UserTemplateEmailRepository,
    private readonly emailServerConfigurationUserService: EmailServerConfigurationUserService,
  ) {}

  /**
   * Tạo campaign mới
   * @param userId ID của người dùng
   * @param createCampaignDto Dữ liệu tạo campaign
   * @returns Thông tin campaign đã tạo
   */
  @Transactional()
  async create(userId: number, createCampaignDto: CreateCampaignDto): Promise<CampaignResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    // Kiểm tra segment hoặc audience
    if (!createCampaignDto.segmentId && (!createCampaignDto.audienceIds || createCampaignDto.audienceIds.length === 0)) {
      throw new BadRequestException('Phải chọn segment hoặc danh sách audience');
    }

    // Tạo campaign
    const campaign = new UserCampaign();
    campaign.userId = userId;
    campaign.title = createCampaignDto.title;
    campaign.description = createCampaignDto.description || '';
    campaign.platform = createCampaignDto.platform;
    campaign.content = createCampaignDto.content;
    campaign.server = createCampaignDto.server;
    campaign.scheduledAt = createCampaignDto.scheduledAt || 0;
    campaign.subject = createCampaignDto.subject || '';
    campaign.status = CampaignStatus.DRAFT;
    campaign.createdAt = now;
    campaign.updatedAt = now;

    const savedCampaign = await this.userCampaignRepository.save(campaign);

    return this.mapToDto(savedCampaign as UserCampaign);
  }

  /**
   * Cập nhật campaign
   * @param userId ID của người dùng
   * @param id ID của campaign
   * @param updateCampaignDto Dữ liệu cập nhật campaign
   * @returns Thông tin campaign đã cập nhật
   */
  async update(userId: number, id: number, updateCampaignDto: UpdateCampaignDto): Promise<CampaignResponseDto> {
    const campaign = await this.userCampaignRepository.findOne({ where: { id, userId } });
    if (!campaign) {
      throw new NotFoundException(`Campaign với ID ${id} không tồn tại`);
    }

    // Kiểm tra trạng thái
    if (campaign.status !== CampaignStatus.DRAFT && campaign.status !== CampaignStatus.SCHEDULED) {
      throw new BadRequestException(`Không thể cập nhật campaign đang chạy hoặc đã hoàn thành`);
    }

    // Cập nhật các trường
    if (updateCampaignDto.title !== undefined) {
      campaign.title = updateCampaignDto.title;
    }

    if (updateCampaignDto.description !== undefined) {
      campaign.description = updateCampaignDto.description;
    }

    if (updateCampaignDto.platform !== undefined) {
      campaign.platform = updateCampaignDto.platform;
    }

    if (updateCampaignDto.content !== undefined) {
      campaign.content = updateCampaignDto.content;
    }

    if (updateCampaignDto.server !== undefined) {
      campaign.server = updateCampaignDto.server;
    }

    if (updateCampaignDto.scheduledAt !== undefined) {
      campaign.scheduledAt = updateCampaignDto.scheduledAt;
    }

    if (updateCampaignDto.subject !== undefined) {
      campaign.subject = updateCampaignDto.subject;
    }

    if (updateCampaignDto.status !== undefined) {
      campaign.status = updateCampaignDto.status;
    }

    campaign.updatedAt = Math.floor(Date.now() / 1000);

    const updatedCampaign = await this.userCampaignRepository.save(campaign);
    return this.mapToDto(updatedCampaign as UserCampaign);
  }

  /**
   * Xóa campaign
   * @param userId ID của người dùng
   * @param id ID của campaign
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(userId: number, id: number): Promise<boolean> {
    const campaign = await this.userCampaignRepository.findOne({ where: { id, userId } });
    if (!campaign) {
      throw new NotFoundException(`Campaign với ID ${id} không tồn tại`);
    }

    // Kiểm tra trạng thái
    if (campaign.status === CampaignStatus.RUNNING) {
      throw new BadRequestException('Không thể xóa campaign đang chạy');
    }

    // Xóa lịch sử campaign
    await this.userCampaignHistoryRepository.delete({ campaignId: id });

    // Xóa campaign
    await this.userCampaignRepository.remove(campaign);
    return true;
  }

  /**
   * Lấy danh sách campaign của người dùng với phân trang và filter
   * @param userId ID của người dùng
   * @param queryDto Tham số query
   * @returns Danh sách campaign với phân trang
   */
  async findAll(userId: number, queryDto: CampaignQueryDto): Promise<PaginatedResult<CampaignResponseDto>> {
    const { page, limit, search, sortBy, sortDirection, status, platform, title } = queryDto;

    // Xây dựng điều kiện where
    const where: any = { userId };

    if (status) {
      where.status = status;
    }

    if (platform) {
      where.platform = platform;
    }

    if (title) {
      where.title = Like(`%${title}%`);
    }

    if (search) {
      where.title = Like(`%${search}%`);
    }

    // Xây dựng options cho query
    const options: FindManyOptions<UserCampaign> = {
      where,
      skip: (page - 1) * limit,
      take: limit,
      order: {
        [sortBy || 'createdAt']: sortDirection || 'DESC',
      },
    };

    // Thực hiện query
    const [campaigns, total] = await this.userCampaignRepository.findAndCount(options);

    // Chuyển đổi thành DTO
    const items = campaigns.map(campaign => this.mapToDto(campaign));

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy thông tin campaign theo ID
   * @param userId ID của người dùng
   * @param id ID của campaign
   * @returns Thông tin campaign
   */
  async findOne(userId: number, id: number): Promise<CampaignResponseDto> {
    const campaign = await this.userCampaignRepository.findOne({ where: { id, userId } });
    if (!campaign) {
      throw new NotFoundException(`Campaign với ID ${id} không tồn tại`);
    }

    const dto = this.mapToDto(campaign);

    // Lấy thống kê
    dto.stats = await this.getCampaignStats(id);

    return dto;
  }

  /**
   * Lấy lịch sử của campaign
   * @param userId ID của người dùng
   * @param id ID của campaign
   * @returns Danh sách lịch sử campaign
   */
  async getCampaignHistory(userId: number, id: number): Promise<CampaignHistoryResponseDto[]> {
    const campaign = await this.userCampaignRepository.findOne({ where: { id, userId } });
    if (!campaign) {
      throw new NotFoundException(`Campaign với ID ${id} không tồn tại`);
    }

    const history = await this.userCampaignHistoryRepository.find({ where: { campaignId: id } });
    return history.map(item => this.mapToHistoryDto(item));
  }

  /**
   * Chạy campaign
   * @param userId ID của người dùng
   * @param id ID của campaign
   * @returns Thông tin campaign đã cập nhật
   */
  @Transactional()
  async runCampaign(userId: number, id: number): Promise<CampaignResponseDto> {
    const campaign = await this.userCampaignRepository.findOne({ where: { id, userId } });
    if (!campaign) {
      throw new NotFoundException(`Campaign với ID ${id} không tồn tại`);
    }

    // Kiểm tra trạng thái
    if (campaign.status !== CampaignStatus.DRAFT && campaign.status !== CampaignStatus.SCHEDULED) {
      throw new BadRequestException(`Campaign đã được chạy hoặc đã hoàn thành`);
    }

    // Cập nhật trạng thái
    campaign.status = CampaignStatus.RUNNING;
    campaign.updatedAt = Math.floor(Date.now() / 1000);
    await this.userCampaignRepository.save(campaign);

    // TODO: Thực hiện gửi campaign (có thể sử dụng queue)
    // Đây là một triển khai giả lập
    try {
      // Lấy danh sách audience
      const audiences = await this.getAudiencesForCampaign(userId, campaign);

      // Tạo lịch sử gửi
      const now = Math.floor(Date.now() / 1000);
      const histories: UserCampaignHistory[] = [];

      for (const audience of audiences) {
        const history = new UserCampaignHistory();
        history.campaignId = campaign.id;
        history.audienceId = audience.id;
        history.status = SendStatus.SENT;
        history.sentAt = now;
        history.createdAt = now;
        histories.push(history);
      }

      await this.userCampaignHistoryRepository.save(histories);

      // Cập nhật trạng thái campaign
      campaign.status = CampaignStatus.COMPLETED;
      campaign.updatedAt = now;
      await this.userCampaignRepository.save(campaign);

      return this.mapToDto(campaign);
    } catch (error) {
      // Xử lý lỗi
      campaign.status = CampaignStatus.FAILED;
      campaign.updatedAt = Math.floor(Date.now() / 1000);
      await this.userCampaignRepository.save(campaign);

      throw error;
    }
  }

  /**
   * Lấy danh sách audience cho campaign
   * @param userId ID của người dùng
   * @param campaign Campaign cần lấy audience
   * @returns Danh sách audience
   */
  private async getAudiencesForCampaign(userId: number, campaign: UserCampaign): Promise<UserAudience[]> {
    // Nếu có segmentId, lấy audience từ segment
    if (campaign.segmentId) {
      const segment = await this.userSegmentRepository.findOne({ where: { id: campaign.segmentId, userId } });
      if (!segment) {
        throw new NotFoundException(`Segment với ID ${campaign.segmentId} không tồn tại`);
      }

      return this.userSegmentService.getAudiencesInSegment(userId, segment);
    }

    // Nếu có audienceIds, lấy audience theo ID
    if (campaign.audienceIds && campaign.audienceIds.length > 0) {
      return this.userAudienceRepository.find({
        where: {
          id: In(campaign.audienceIds),
          userId,
        },
      });
    }

    return [];
  }

  /**
   * Lấy thống kê của campaign
   * @param campaignId ID của campaign
   * @returns Thống kê campaign
   */
  private async getCampaignStats(campaignId: number): Promise<CampaignStatsDto> {
    const stats = new CampaignStatsDto();

    // Đếm số lượng theo trạng thái
    const history = await this.userCampaignHistoryRepository.find({ where: { campaignId } });

    stats.totalRecipients = history.length;
    stats.sent = history.filter(h => h.status === SendStatus.SENT || h.status === SendStatus.DELIVERED || h.status === SendStatus.OPENED || h.status === SendStatus.CLICKED).length;
    stats.delivered = history.filter(h => h.status === SendStatus.DELIVERED || h.status === SendStatus.OPENED || h.status === SendStatus.CLICKED).length;
    stats.opened = history.filter(h => h.status === SendStatus.OPENED || h.status === SendStatus.CLICKED).length;
    stats.clicked = history.filter(h => h.status === SendStatus.CLICKED).length;
    stats.failed = history.filter(h => h.status === SendStatus.FAILED).length;

    return stats;
  }

  /**
   * Chuyển đổi entity thành DTO
   * @param campaign Entity campaign
   * @returns DTO campaign
   */
  private mapToDto(campaign: UserCampaign): CampaignResponseDto {
    const dto = new CampaignResponseDto();
    dto.id = campaign.id;
    dto.title = campaign.title;
    dto.description = campaign.description;
    dto.platform = campaign.platform as any;
    dto.content = campaign.content;
    dto.server = campaign.server;
    dto.scheduledAt = campaign.scheduledAt;
    dto.subject = campaign.subject;
    dto.status = campaign.status as any;
    dto.createdAt = campaign.createdAt;
    dto.updatedAt = campaign.updatedAt;
    return dto;
  }

  /**
   * Chuyển đổi entity lịch sử thành DTO
   * @param history Entity lịch sử campaign
   * @returns DTO lịch sử campaign
   */
  private mapToHistoryDto(history: UserCampaignHistory): CampaignHistoryResponseDto {
    const dto = new CampaignHistoryResponseDto();
    dto.id = history.id;
    dto.campaignId = history.campaignId;
    dto.audienceId = history.audienceId;
    dto.status = history.status as any;
    dto.sentAt = history.sentAt;
    dto.createdAt = history.createdAt;
    return dto;
  }

  /**
   * Lấy danh sách chiến dịch tích hợp Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách chiến dịch tích hợp với phân trang
   */
  async getZaloIntegrationCampaigns(userId: number, oaId: string, queryDto: any): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy danh sách chiến dịch có tích hợp Zalo
      // Sử dụng raw query hoặc JSON query thay vì MongoDB-style operators
      const campaigns = await this.userCampaignRepository.find({
        where: {
          userId,
        },
      }).then(campaigns =>
        campaigns.filter(campaign => {
          // Kiểm tra nếu content là JSON object
          try {
            if (typeof campaign.content === 'string') {
              const content = JSON.parse(campaign.content);
              return content.channels?.includes('zalo') && content.zaloConfig?.oaId === oaId;
            }
          } catch (e) {
            // Nếu không phải JSON hợp lệ, bỏ qua
          }
          return false;
        })
      );

      // Áp dụng phân trang
      const { page, limit } = queryDto;
      const startIndex = (page - 1) * limit;
      const endIndex = Math.min(startIndex + limit, campaigns.length);
      const items = campaigns.slice(startIndex, endIndex);

      // Chuyển đổi thành DTO
      const mappedItems = items.map(campaign => {
        // Parse content để lấy thông tin
        let parsedContent: {type?: string, channels?: string[]} = {
          type: 'broadcast',
          channels: ['zalo']
        };
        try {
          if (typeof campaign.content === 'string') {
            parsedContent = JSON.parse(campaign.content);
          }
        } catch (e) {
          // Giữ giá trị mặc định nếu parse thất bại
        }

        return {
          id: campaign.id,
          name: campaign.title,
          description: campaign.description,
          type: parsedContent.type || 'broadcast',
          status: campaign.status,
          channels: parsedContent.channels || ['zalo'],
          createdAt: campaign.createdAt,
          updatedAt: campaign.updatedAt,
        };
      });

      return {
        items: mappedItems,
        meta: {
          totalItems: campaigns.length,
          itemCount: mappedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(campaigns.length / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể lấy danh sách chiến dịch tích hợp Zalo');
    }
  }

  /**
   * Lấy thông tin chi tiết chiến dịch tích hợp Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @returns Thông tin chi tiết chiến dịch tích hợp
   */
  async getZaloIntegrationCampaignDetail(userId: number, oaId: string, id: number): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy thông tin chiến dịch
      const campaign = await this.userCampaignRepository.findOne({
        where: {
          id,
          userId,
        },
      });

      if (!campaign) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy chiến dịch tích hợp Zalo');
      }

      // Kiểm tra nếu campaign có tích hợp Zalo
      let contentObj: any = null;
      try {
        if (typeof campaign.content === 'string') {
          contentObj = JSON.parse(campaign.content);
        }
      } catch (e) {
        // Nếu không phải JSON hợp lệ, bỏ qua
      }

      if (!contentObj || !contentObj.channels?.includes('zalo') || contentObj.zaloConfig?.oaId !== oaId) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy chiến dịch tích hợp Zalo');
      }

      // Chuyển đổi thành DTO
      return {
        id: campaign.id,
        name: campaign.title,
        description: campaign.description,
        type: contentObj?.type || 'broadcast',
        status: campaign.status,
        channels: contentObj?.channels || ['zalo'],
        content: campaign.content,
        zaloConfig: contentObj?.zaloConfig,
        emailConfig: contentObj?.emailConfig,
        smsConfig: contentObj?.smsConfig,
        createdAt: campaign.createdAt,
        updatedAt: campaign.updatedAt,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể lấy thông tin chi tiết chiến dịch tích hợp Zalo');
    }
  }

  /**
   * Tạo chiến dịch tích hợp Zalo mới
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param createDto Dữ liệu tạo chiến dịch
   * @returns Chiến dịch tích hợp đã tạo
   */
  async createZaloIntegrationCampaign(userId: number, oaId: string, createDto: any): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Tạo chiến dịch mới
      const now = Math.floor(Date.now() / 1000);
      const campaign = new UserCampaign();
      campaign.userId = userId;
      campaign.title = createDto.name;
      campaign.description = createDto.description || '';
      campaign.platform = 'multi';

      // Tạo nội dung dưới dạng chuỗi JSON
      const contentObj = {
        type: createDto.type || 'broadcast',
        channels: createDto.channels || ['zalo'],
        zaloConfig: {
          oaId,
          ...createDto.zaloConfig,
        },
        emailConfig: createDto.emailConfig,
        smsConfig: createDto.smsConfig,
      };
      campaign.content = JSON.stringify(contentObj);
      campaign.scheduledAt = createDto.scheduledAt || 0;
      campaign.status = CampaignStatus.DRAFT;
      campaign.createdAt = now;
      campaign.updatedAt = now;

      const savedCampaign = await this.userCampaignRepository.save(campaign);

      // Parse content để lấy thông tin
      let parsedContent: {type?: string, channels?: string[], zaloConfig?: any, emailConfig?: any, smsConfig?: any} = {
        type: 'broadcast',
        channels: ['zalo']
      };
      try {
        if (typeof savedCampaign.content === 'string') {
          parsedContent = JSON.parse(savedCampaign.content);
        }
      } catch (e) {
        // Giữ giá trị mặc định nếu parse thất bại
      }

      return {
        id: savedCampaign.id,
        name: savedCampaign.title,
        description: savedCampaign.description,
        type: parsedContent.type || 'broadcast',
        status: savedCampaign.status,
        channels: parsedContent.channels || ['zalo'],
        createdAt: savedCampaign.createdAt,
        updatedAt: savedCampaign.updatedAt,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể tạo chiến dịch tích hợp Zalo');
    }
  }

  /**
   * Cập nhật chiến dịch tích hợp Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @param updateDto Dữ liệu cập nhật
   * @returns Chiến dịch tích hợp đã cập nhật
   */
  async updateZaloIntegrationCampaign(userId: number, oaId: string, id: number, updateDto: any): Promise<any> {
    try {
      // Lấy thông tin chiến dịch
      const campaign = await this.getZaloIntegrationCampaignDetail(userId, oaId, id);

      // Kiểm tra trạng thái
      if (campaign.status !== CampaignStatus.DRAFT && campaign.status !== CampaignStatus.SCHEDULED) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể cập nhật chiến dịch đang chạy hoặc đã hoàn thành'
        );
      }

      // Cập nhật chiến dịch
      const updateData: any = {
        updatedAt: Math.floor(Date.now() / 1000),
      };

      if (updateDto.name !== undefined) {
        updateData.title = updateDto.name;
      }

      if (updateDto.description !== undefined) {
        updateData.description = updateDto.description;
      }

      if (updateDto.scheduledAt !== undefined) {
        updateData.scheduledAt = updateDto.scheduledAt;
      }

      if (updateDto.status !== undefined) {
        updateData.status = updateDto.status;
      }

      // Cập nhật nội dung
      if (updateDto.content || updateDto.zaloConfig || updateDto.emailConfig || updateDto.smsConfig) {
        const content = { ...campaign.content };

        if (updateDto.content?.type !== undefined) {
          content.type = updateDto.content.type;
        }

        if (updateDto.content?.channels !== undefined) {
          content.channels = updateDto.content.channels;
        }

        if (updateDto.zaloConfig !== undefined) {
          content.zaloConfig = {
            ...content.zaloConfig,
            ...updateDto.zaloConfig,
          };
        }

        if (updateDto.emailConfig !== undefined) {
          content.emailConfig = {
            ...content.emailConfig,
            ...updateDto.emailConfig,
          };
        }

        if (updateDto.smsConfig !== undefined) {
          content.smsConfig = {
            ...content.smsConfig,
            ...updateDto.smsConfig,
          };
        }

        updateData.content = content;
      }

      await this.userCampaignRepository.save({id, ...updateData});

      return {
        id,
        name: updateDto.name || campaign.name,
        description: updateDto.description || campaign.description,
        type: updateDto.content?.type || campaign.type,
        status: updateDto.status || campaign.status,
        channels: updateDto.content?.channels || campaign.channels,
        updatedAt: Math.floor(Date.now() / 1000),
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể cập nhật chiến dịch tích hợp Zalo');
    }
  }

  /**
   * Xóa chiến dịch tích hợp Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @returns true nếu xóa thành công
   */
  async deleteZaloIntegrationCampaign(userId: number, oaId: string, id: number): Promise<boolean> {
    try {
      // Lấy thông tin chiến dịch
      const campaign = await this.getZaloIntegrationCampaignDetail(userId, oaId, id);

      // Kiểm tra trạng thái
      if (campaign.status === CampaignStatus.RUNNING) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể xóa chiến dịch đang chạy'
        );
      }

      // Xóa lịch sử chiến dịch
      await this.userCampaignHistoryRepository.delete({ campaignId: id });

      // Xóa chiến dịch
      await this.userCampaignRepository.delete(id);

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể xóa chiến dịch tích hợp Zalo');
    }
  }

  /**
   * Thực thi chiến dịch tích hợp Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @returns Kết quả thực thi
   */
  async executeZaloIntegrationCampaign(userId: number, oaId: string, id: number): Promise<any> {
    try {
      // Lấy thông tin chiến dịch
      const campaign = await this.getZaloIntegrationCampaignDetail(userId, oaId, id);

      // Kiểm tra trạng thái
      if (campaign.status !== CampaignStatus.DRAFT && campaign.status !== CampaignStatus.SCHEDULED) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Chiến dịch đã được chạy hoặc đã hoàn thành'
        );
      }

      // Cập nhật trạng thái
      const now = Math.floor(Date.now() / 1000);
      // Lấy campaign hiện tại và cập nhật
      const campaignToUpdate = await this.userCampaignRepository.findOne({ where: { id } });
      if (campaignToUpdate) {
        campaignToUpdate.status = CampaignStatus.RUNNING;
        campaignToUpdate.updatedAt = now;
        await this.userCampaignRepository.save(campaignToUpdate);
      }

      // Thực thi chiến dịch
      // TODO: Thực hiện gửi chiến dịch (có thể sử dụng queue)
      // Đây là một triển khai giả lập
      try {
        // Xử lý từng kênh
        const channels = campaign.channels || ['zalo'];
        const results: Array<{channel: string, status: string, result: any}> = [];

        for (const channel of channels) {
          if (channel === 'zalo') {
            // Thực thi chiến dịch Zalo
            if (campaign.zaloConfig?.segmentId) {
              // Gửi cho phân đoạn
              const result = await this.zaloService.executeZaloCampaign(userId, oaId, campaign.zaloConfig.segmentId);
              results.push({
                channel: 'zalo',
                status: 'success',
                result,
              });
            } else if (campaign.zaloConfig?.messageTemplateId) {
              // Gửi tin nhắn mẫu
              const result = await this.zaloService.batchSendZaloTemplateMessage(
                userId,
                oaId,
                campaign.zaloConfig.messageTemplateId,
                campaign.zaloConfig.followerIds || [],
                campaign.zaloConfig.templateData
              );
              results.push({
                channel: 'zalo',
                status: 'success',
                result,
              });
            }
          } else if (channel === 'email') {
            // TODO: Thực thi chiến dịch Email
            results.push({
              channel: 'email',
              status: 'success',
              result: { sent: 0 },
            });
          } else if (channel === 'sms') {
            // TODO: Thực thi chiến dịch SMS
            results.push({
              channel: 'sms',
              status: 'success',
              result: { sent: 0 },
            });
          }
        }

        // Cập nhật trạng thái chiến dịch
        const campaignToComplete = await this.userCampaignRepository.findOne({ where: { id } });
        if (campaignToComplete) {
          campaignToComplete.status = CampaignStatus.COMPLETED;
          campaignToComplete.updatedAt = Math.floor(Date.now() / 1000);
          await this.userCampaignRepository.save(campaignToComplete);
        }

        return {
          id,
          status: CampaignStatus.COMPLETED,
          startedAt: now,
          results,
        };
      } catch (error) {
        // Xử lý lỗi
        const campaignToFail = await this.userCampaignRepository.findOne({ where: { id } });
        if (campaignToFail) {
          campaignToFail.status = CampaignStatus.FAILED;
          campaignToFail.updatedAt = Math.floor(Date.now() / 1000);
          await this.userCampaignRepository.save(campaignToFail);
        }

        throw error;
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể thực thi chiến dịch tích hợp Zalo');
    }
  }

  /**
   * Lấy lịch sử thực thi chiến dịch tích hợp Zalo
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của chiến dịch
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử thực thi với phân trang
   */
  async getZaloIntegrationCampaignHistory(
    userId: number,
    oaId: string,
    id: number,
    queryDto: any
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập chiến dịch
      await this.getZaloIntegrationCampaignDetail(userId, oaId, id);

      // Lấy lịch sử chiến dịch
      const history = await this.userCampaignHistoryRepository.find({
        where: { campaignId: id },
      });

      // Áp dụng phân trang
      const { page, limit } = queryDto;
      const startIndex = (page - 1) * limit;
      const endIndex = Math.min(startIndex + limit, history.length);
      const items = history.slice(startIndex, endIndex);

      // Chuyển đổi thành DTO với các trường bổ sung
      const mappedItems = items.map(item => {
        // Lấy thông tin cơ bản từ entity
        const baseInfo = {
          id: item.id,
          campaignId: item.campaignId,
          status: item.status,
          createdAt: item.createdAt,
        };

        // Thêm các trường bổ sung (giả định từ dữ liệu khác hoặc mặc định)
        return {
          ...baseInfo,
          channel: 'zalo', // Mặc định là zalo vì đây là tích hợp Zalo
          totalRecipients: 0, // Giá trị mặc định
          successCount: 0, // Giá trị mặc định
          failedCount: 0, // Giá trị mặc định
          startedAt: item.sentAt || item.createdAt,
          completedAt: item.sentAt ? item.sentAt + 60 : null, // Giả định hoàn thành sau 1 phút
        };
      });

      return {
        items: mappedItems,
        meta: {
          totalItems: history.length,
          itemCount: mappedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(history.length / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể lấy lịch sử thực thi chiến dịch tích hợp Zalo');
    }
  }

  /**
   * Tạo campaign từ template với format request tùy chỉnh
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo campaign từ template
   * @returns Thông tin campaign đã tạo
   */
  @Transactional()
  async createTemplateCampaign(userId: number, createDto: CreateTemplateCampaignDto): Promise<CreateTemplateCampaignResponseDto> {
    try {
      // 1. Validate và lấy thông tin template
      const templateId = parseInt(createDto.templateId);
      if (isNaN(templateId)) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Template ID phải là số hợp lệ');
      }

      const template = await this.userTemplateEmailRepository.findOne({
        where: { id: templateId, userId }
      });

      if (!template) {
        throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy template email');
      }

      // 2. Validate và lấy thông tin email server
      const emailServerId = parseInt(createDto.emailServerId);
      if (isNaN(emailServerId)) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Email Server ID phải là số hợp lệ');
      }

      const emailServer = await this.emailServerConfigurationUserService.findById(emailServerId, userId);
      if (!emailServer) {
        throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy cấu hình email server');
      }

      // 3. Xử lý thời gian gửi
      let scheduledAtTimestamp = 0;
      if (createDto.scheduledAt) {
        const scheduledDate = new Date(createDto.scheduledAt);
        if (isNaN(scheduledDate.getTime())) {
          throw new AppException(ErrorCode.VALIDATION_ERROR, 'Thời gian gửi không hợp lệ');
        }

        scheduledAtTimestamp = Math.floor(scheduledDate.getTime() / 1000);

        // Kiểm tra thời gian phải trong tương lai
        const now = Math.floor(Date.now() / 1000);
        if (scheduledAtTimestamp <= now) {
          throw new AppException(ErrorCode.VALIDATION_ERROR, 'Thời gian gửi phải trong tương lai');
        }
      }

      // 4. Lấy danh sách audience từ segments và audienceIds
      let totalAudienceCount = 0;
      const audienceIds: number[] = [];

      // Xử lý segments
      if (createDto.segmentIds && createDto.segmentIds.length > 0) {
        for (const segmentIdStr of createDto.segmentIds) {
          const segmentId = parseInt(segmentIdStr);
          if (isNaN(segmentId)) {
            throw new AppException(ErrorCode.VALIDATION_ERROR, `Segment ID "${segmentIdStr}" không hợp lệ`);
          }

          const segment = await this.userSegmentRepository.findOne({
            where: { id: segmentId, userId }
          });

          if (!segment) {
            throw new AppException(ErrorCode.NOT_FOUND, `Không tìm thấy segment với ID ${segmentId}`);
          }

          // Lấy audiences từ segment
          const segmentAudiences = await this.userSegmentService.getAudiencesBySegment(userId, segmentId);
          segmentAudiences.forEach(audience => {
            if (!audienceIds.includes(audience.id)) {
              audienceIds.push(audience.id);
            }
          });
        }
      }

      // Xử lý audienceIds trực tiếp
      if (createDto.audienceIds && createDto.audienceIds.length > 0) {
        for (const audienceIdStr of createDto.audienceIds) {
          const audienceId = parseInt(audienceIdStr);
          if (isNaN(audienceId)) {
            throw new AppException(ErrorCode.VALIDATION_ERROR, `Audience ID "${audienceIdStr}" không hợp lệ`);
          }

          const audience = await this.userAudienceRepository.findOne({
            where: { id: audienceId, userId }
          });

          if (!audience) {
            throw new AppException(ErrorCode.NOT_FOUND, `Không tìm thấy audience với ID ${audienceId}`);
          }

          if (!audienceIds.includes(audienceId)) {
            audienceIds.push(audienceId);
          }
        }
      }

      totalAudienceCount = audienceIds.length;

      if (totalAudienceCount === 0) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Phải chọn ít nhất một segment hoặc audience để gửi campaign');
      }

      // 5. Tạo campaign
      const now = Math.floor(Date.now() / 1000);
      const campaign = new UserCampaign();
      campaign.userId = userId;
      campaign.title = createDto.name;
      campaign.description = createDto.description || '';
      campaign.platform = 'email';
      campaign.subject = template.subject;
      campaign.content = template.htmlContent || template.content || '';

      // Lưu thông tin server configuration
      campaign.server = {
        emailServerId: emailServer.id,
        serverName: emailServer.serverName,
        host: emailServer.host,
        port: emailServer.port,
        username: emailServer.username,
        useSsl: emailServer.useSsl,
        useStartTls: emailServer.useStartTls
      };

      campaign.scheduledAt = scheduledAtTimestamp;
      campaign.status = scheduledAtTimestamp > 0 ? CampaignStatus.SCHEDULED : CampaignStatus.DRAFT;
      campaign.createdAt = now;
      campaign.updatedAt = now;

      // Lưu thông tin template và audience
      campaign.audienceIds = audienceIds;

      const savedCampaign = await this.userCampaignRepository.save(campaign);

      // 6. Trả về response
      return {
        campaignId: savedCampaign.id,
        name: savedCampaign.title,
        status: savedCampaign.status,
        audienceCount: totalAudienceCount,
        scheduledAt: scheduledAtTimestamp > 0 ? scheduledAtTimestamp : undefined,
        template: {
          id: template.id,
          name: template.name,
          subject: template.subject
        },
        emailServer: {
          id: emailServer.id,
          serverName: emailServer.serverName,
          host: emailServer.host
        }
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, `Không thể tạo campaign từ template: ${error.message}`);
    }
  }
}
