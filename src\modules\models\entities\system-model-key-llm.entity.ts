import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng system_model_key_llm trong cơ sở dữ liệu
 * Bảng ánh xạ nhiều-nhiều giữa system_models và system_key_llm
 */
@Entity('system_model_key_llm')
export class SystemModelKeyLlm {
  /**
   * ID của system_models
   */
  @PrimaryColumn({ name: 'model_id', type: 'uuid' })
  modelId: string;

  /**
   * ID của system_key_llm
   */
  @PrimaryColumn({ name: 'llm_key_id', type: 'uuid' })
  llmKeyId: string;
}
