import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { TypeAgentAgentSystem } from '@modules/agent/entities';

/**
 * Repository cho TypeAgentAgentSystem
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến ánh xạ type agent và agent system
 */
@Injectable()
export class TypeAgentAgentSystemRepository extends Repository<TypeAgentAgentSystem> {
  private readonly logger = new Logger(TypeAgentAgentSystemRepository.name);

  constructor(private dataSource: DataSource) {
    super(TypeAgentAgentSystem, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho TypeAgentAgentSystem
   * @returns SelectQueryBuilder cho TypeAgentAgentSystem
   */
  private createBaseQuery(): SelectQueryBuilder<TypeAgentAgentSystem> {
    return this.createQueryBuilder('typeAgentAgentSystem');
  }

  /**
   * Tìm ánh xạ theo type ID và agent ID
   * @param typeId ID của type agent
   * @param agentId ID của agent system
   * @returns TypeAgentAgentSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findByTypeAndAgent(typeId: number, agentId: string): Promise<TypeAgentAgentSystem | null> {
    return this.createBaseQuery()
      .where('typeAgentAgentSystem.typeId = :typeId', { typeId })
      .andWhere('typeAgentAgentSystem.agentId = :agentId', { agentId })
      .getOne();
  }

  /**
   * Lấy danh sách agent systems của một type agent
   * @param typeId ID của type agent
   * @returns Danh sách agent systems
   */
  async findAgentsByTypeId(typeId: number): Promise<any[]> {
    return this.dataSource
      .createQueryBuilder()
      .select([
        'ags.id',
        'ags.nameCode',
        'ags.description',
        'ags.active',
        'ags.isSupervisor'
      ])
      .from('type_agent_agent_system', 'taas')
      .innerJoin('agents_system', 'ags', 'taas.agent_id = ags.id')
      .where('taas.type_id = :typeId', { typeId })
      .andWhere('ags.deleted_by IS NULL')
      .orderBy('ags.nameCode', 'ASC')
      .getRawMany();
  }

  /**
   * Lấy danh sách type agents sử dụng một agent system
   * @param agentId ID của agent system
   * @returns Danh sách type agents
   */
  async findTypesByAgentId(agentId: string): Promise<any[]> {
    return this.dataSource
      .createQueryBuilder()
      .select([
        'ta.id',
        'ta.name',
        'ta.description',
        'ta.status'
      ])
      .from('type_agent_agent_system', 'taas')
      .innerJoin('type_agents', 'ta', 'taas.type_id = ta.id')
      .where('taas.agent_id = :agentId', { agentId })
      .andWhere('ta.deleted_at IS NULL')
      .orderBy('ta.name', 'ASC')
      .getRawMany();
  }

  /**
   * Liên kết type agent với agent systems
   * @param typeId ID của type agent
   * @param agentIds Danh sách ID của agent systems
   */
  async linkTypeWithAgents(typeId: number, agentIds: string[]): Promise<void> {
    try {
      // Xóa tất cả liên kết cũ
      await this.createQueryBuilder()
        .delete()
        .from(TypeAgentAgentSystem)
        .where('typeId = :typeId', { typeId })
        .execute();

      // Thêm liên kết mới
      if (agentIds && agentIds.length > 0) {
        const linkData = agentIds.map(agentId => ({
          typeId: typeId,
          agentId: agentId,
        }));

        await this.createQueryBuilder()
          .insert()
          .into(TypeAgentAgentSystem)
          .values(linkData)
          .execute();
      }
    } catch (error) {
      this.logger.error(`Lỗi khi liên kết type agent với agent systems: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa liên kết giữa type agent và agent system
   * @param typeId ID của type agent
   * @param agentId ID của agent system
   * @returns true nếu xóa thành công
   */
  async unlinkTypeFromAgent(typeId: number, agentId: string): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(TypeAgentAgentSystem)
        .where('typeId = :typeId', { typeId })
        .andWhere('agentId = :agentId', { agentId })
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa liên kết type-agent: ${error.message}`);
      return false;
    }
  }

  /**
   * Xóa tất cả liên kết của một type agent
   * @param typeId ID của type agent
   * @returns true nếu xóa thành công
   */
  async unlinkAllAgentsFromType(typeId: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(TypeAgentAgentSystem)
        .where('typeId = :typeId', { typeId })
        .execute();

      return true; // Luôn trả về true vì có thể không có liên kết nào
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tất cả liên kết agent của type ${typeId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Đếm số lượng agent systems của một type agent
   * @param typeId ID của type agent
   * @returns Số lượng agent systems
   */
  async countAgentsByTypeId(typeId: number): Promise<number> {
    try {
      const result = await this.createBaseQuery()
        .where('typeAgentAgentSystem.typeId = :typeId', { typeId })
        .getCount();

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm agent systems của type ${typeId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Đếm số lượng type agents sử dụng một agent system
   * @param agentId ID của agent system
   * @returns Số lượng type agents
   */
  async countTypesByAgentId(agentId: string): Promise<number> {
    try {
      const result = await this.createBaseQuery()
        .where('typeAgentAgentSystem.agentId = :agentId', { agentId })
        .getCount();

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm type agents của agent ${agentId}: ${error.message}`);
      return 0;
    }
  }
}
