import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { GHN_PRINT_FORMATS } from '@modules/business/constants/ghn.constants';
import { GHTK_PAPER_SIZES, GHTK_ORIENTATIONS } from '@modules/business/constants/ghtk.constants';

/**
 * Enum cho khổ giấy in GHTK
 */
export enum PrintPaperSize {
  A5 = 'A5',
  A6 = 'A6'
}

/**
 * Enum cho hướng in GHTK
 */
export enum PrintOrientation {
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape'
}

/**
 * Enum cho format in GHN
 */
export enum GHNPrintFormat {
  A5 = 'A5',
  THERMAL_80x80 = '80x80',
  THERMAL_52x70 = '52x70'
}

/**
 * DTO cho query parameters của API in đơn hàng
 */
export class PrintOrderQueryDto {
  @ApiProperty({
    description: 'Khổ giấy in (chỉ áp dụng cho GHTK). GHN mặc định A5.',
    enum: PrintPaperSize,
    required: false,
    default: PrintPaperSize.A6,
    example: PrintPaperSize.A6
  })
  @IsOptional()
  @IsEnum(PrintPaperSize, { message: 'paperSize phải là A5 hoặc A6' })
  paperSize?: PrintPaperSize = PrintPaperSize.A6;

  @ApiProperty({
    description: 'Hướng in (chỉ áp dụng cho GHTK). GHN mặc định portrait.',
    enum: PrintOrientation,
    required: false,
    default: PrintOrientation.PORTRAIT,
    example: PrintOrientation.PORTRAIT
  })
  @IsOptional()
  @IsEnum(PrintOrientation, { message: 'orientation phải là portrait hoặc landscape' })
  orientation?: PrintOrientation = PrintOrientation.PORTRAIT;

  @ApiProperty({
    description: 'Format in cho GHN (A5, 80x80, 52x70). Chỉ áp dụng cho GHN.',
    enum: GHNPrintFormat,
    required: false,
    default: GHNPrintFormat.A5,
    example: GHNPrintFormat.A5
  })
  @IsOptional()
  @IsEnum(GHNPrintFormat, { message: 'ghnFormat phải là A5, 80x80 hoặc 52x70' })
  ghnFormat?: GHNPrintFormat = GHNPrintFormat.A5;
}
