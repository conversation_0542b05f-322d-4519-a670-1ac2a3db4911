/**
 * Utility để format kích thước file thành định dạng human-readable
 */
export class FileSizeFormatter {
  /**
   * Chuyển đổi bytes thành định dạng human-readable
   * @param bytes Số bytes
   * @param decimals Số chữ số thập phân (mặc định: 2)
   * @returns Chuỗi định dạng (ví dụ: "1.5 GB")
   */
  static formatBytes(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  /**
   * Chuyển đổi bytes thành định dạng human-readable (tiếng Việt)
   * @param bytes Số bytes
   * @param decimals Số chữ số thập phân (mặc định: 2)
   * @returns Chuỗi định dạng (ví dụ: "1,5 GB")
   */
  static formatBytesVi(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const value = (bytes / Math.pow(k, i)).toFixed(dm);

    // Chuyển đổi dấu chấm thành dấu phẩy cho định dạng Việt Nam
    return value.replace('.', ',') + ' ' + sizes[i];
  }

  /**
   * Tính phần trăm sử dụng
   * @param used Dung lượng đã sử dụng
   * @param total Tổng dung lượng
   * @returns Phần trăm sử dụng (0-100)
   */
  static calculateUsagePercentage(used: number, total: number): number {
    if (total === 0) return 0;
    return Math.round((used / total) * 100);
  }

  /**
   * Kiểm tra xem dung lượng có vượt quá giới hạn không
   * @param used Dung lượng đã sử dụng
   * @param limit Giới hạn dung lượng
   * @returns true nếu vượt quá giới hạn
   */
  static isOverLimit(used: number, limit: number): boolean {
    return used > limit;
  }

  /**
   * Tính dung lượng còn lại
   * @param used Dung lượng đã sử dụng
   * @param limit Giới hạn dung lượng
   * @returns Dung lượng còn lại (có thể âm nếu vượt quá)
   */
  static calculateRemaining(used: number, limit: number): number {
    return limit - used;
  }
}
