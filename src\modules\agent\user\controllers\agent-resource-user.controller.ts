import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { ApiResponseDto } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { AgentResourceUserService } from '@modules/agent/user/services';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AddAgentMediaDto,
  AddAgentProductDto,
  AddAgentUrlDto,
  AgentMediaQueryDto,
  AgentMediaResponseDto,
  AgentProductQueryDto,
  AgentProductResponseDto,
  AgentUrlQueryDto,
  AgentUrlResponseDto,
} from '../dto/resource';

/**
 * Controller xử lý các API endpoint cho tài nguyên của Agent
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  AgentMediaResponseDto,
  AgentUrlResponseDto,
  AgentProductResponseDto,
  ApiResponseDto
)
export class AgentResourceUserController {
  constructor(private readonly agentResourceUserService: AgentResourceUserService) { }

  // /**
  //  * Lấy danh sách media của agent
  //  * @param userId ID của người dùng
  //  * @param id ID của agent
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách media có phân trang
  //  */
  // @Get(':id/media')
  // @ApiOperation({ summary: 'Lấy danh sách media của agent' })
  // @ApiParam({ name: 'id', description: 'ID của agent' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Lấy danh sách media thành công',
  //   schema: ApiResponseDto.getPaginatedSchema(AgentMediaResponseDto),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async getAgentMedia(
  //   @CurrentUser('id') userId: number,
  //   @Param('id') id: string,
  //   @Query() queryDto: AgentMediaQueryDto,
  // ) {
  //   const result = await this.agentResourceUserService.getAgentMedia(id, userId, queryDto);
  //   return ApiResponseDto.paginated(result);
  // }

  // /**
  //  * Cập nhật danh sách media của agent
  //  * @param userId ID của người dùng
  //  * @param id ID của agent
  //  * @param updateDto Thông tin cập nhật
  //  * @returns Thông báo thành công
  //  */
  // @Put(':id/media')
  // @ApiOperation({ summary: 'Cập nhật danh sách media của agent' })
  // @ApiParam({ name: 'id', description: 'ID của agent' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Cập nhật danh sách media thành công',
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async updateAgentMedia(
  //   @CurrentUser('id') userId: number,
  //   @Param('id') id: string,
  //   @Body() updateDto: AddAgentMediaDto,
  // ) {
  //   await this.agentResourceUserService.updateAgentMedia(id, userId, updateDto);
  //   return ApiResponseDto.success(null, 'Cập nhật danh sách media thành công');
  // }

  // /**
  //  * Lấy danh sách URL của agent
  //  * @param userId ID của người dùng
  //  * @param id ID của agent
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách URL có phân trang
  //  */
  // @Get(':id/url')
  // @ApiOperation({ summary: 'Lấy danh sách URL của agent' })
  // @ApiParam({ name: 'id', description: 'ID của agent' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Lấy danh sách URL thành công',
  //   schema: ApiResponseDto.getPaginatedSchema(AgentUrlResponseDto),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async getAgentUrl(
  //   @CurrentUser('id') userId: number,
  //   @Param('id') id: string,
  //   @Query() queryDto: AgentUrlQueryDto,
  // ) {
  //   const result = await this.agentResourceUserService.getAgentUrl(id, userId, queryDto);
  //   return ApiResponseDto.paginated(result);
  // }

  // /**
  //  * Cập nhật danh sách URL của agent
  //  * @param userId ID của người dùng
  //  * @param id ID của agent
  //  * @param updateDto Thông tin cập nhật
  //  * @returns Thông báo thành công
  //  */
  // @Put(':id/url')
  // @ApiOperation({ summary: 'Cập nhật danh sách URL của agent' })
  // @ApiParam({ name: 'id', description: 'ID của agent' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Cập nhật danh sách URL thành công',
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.URL_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async updateAgentUrl(
  //   @CurrentUser('id') userId: number,
  //   @Param('id') id: string,
  //   @Body() updateDto: AddAgentUrlDto,
  // ) {
  //   await this.agentResourceUserService.updateAgentUrl(id, userId, updateDto);
  //   return ApiResponseDto.success(null, 'Cập nhật danh sách URL thành công');
  // }

  // /**
  //  * Lấy danh sách sản phẩm của agent
  //  * @param userId ID của người dùng
  //  * @param id ID của agent
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách sản phẩm có phân trang
  //  */
  // @Get(':id/product')
  // @ApiOperation({ summary: 'Lấy danh sách sản phẩm của agent' })
  // @ApiParam({ name: 'id', description: 'ID của agent' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Lấy danh sách sản phẩm thành công',
  //   schema: ApiResponseDto.getPaginatedSchema(AgentProductResponseDto),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async getAgentProduct(
  //   @CurrentUser('id') userId: number,
  //   @Param('id') id: string,
  //   @Query() queryDto: AgentProductQueryDto,
  // ) {
  //   const result = await this.agentResourceUserService.getAgentProduct(id, userId, queryDto);
  //   return ApiResponseDto.paginated(result);
  // }

  // /**
  //  * Cập nhật danh sách sản phẩm của agent
  //  * @param userId ID của người dùng
  //  * @param id ID của agent
  //  * @param updateDto Thông tin cập nhật
  //  * @returns Thông báo thành công
  //  */
  // @Put(':id/product')
  // @ApiOperation({ summary: 'Cập nhật danh sách sản phẩm của agent' })
  // @ApiParam({ name: 'id', description: 'ID của agent' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Cập nhật danh sách sản phẩm thành công',
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async updateAgentProduct(
  //   @CurrentUser('id') userId: number,
  //   @Param('id') id: string,
  //   @Body() updateDto: AddAgentProductDto,
  // ) {
  //   await this.agentResourceUserService.updateAgentProduct(id, userId, updateDto);
  //   return ApiResponseDto.success(null, 'Cập nhật danh sách sản phẩm thành công');
  // }
}
