import { ProviderFineTuneEnum } from '@/modules/models/constants';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo mới user data fine tune
 */
export class CreateUserDataFineTuneDto {
  /**
   * Tên của bộ dữ liệu fine-tune
   */
  @ApiProperty({
    description: 'Tên của bộ dữ liệu fine-tune',
    example: 'Customer Support Dataset',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Mô tả nội dung bộ dữ liệu
   */
  @ApiPropertyOptional({
    description: 'Mô tả nội dung bộ dữ liệu',
    example: 'Dataset chứa các cuộc hội thoại hỗ trợ khách hàng',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * <PERSON>hà cung cấp AI
   */
  @ApiProperty({
    description: 'Nhà cung cấp AI',
    example: ProviderFineTuneEnum.OPENAI,
    enum: ProviderFineTuneEnum,
  })
  @IsEnum(ProviderFineTuneEnum)
  @IsNotEmpty()
  provider: ProviderFineTuneEnum;

  /**
   * Tập dữ liệu huấn luyện, định dạng JSON
   */
  @ApiProperty({
    description: 'Tập dữ liệu huấn luyện, định dạng JSON',
    example: 'application/jsonl'
  })
  @IsString()
  @IsNotEmpty()
  trainDataset: string;

  /**
   * Tập dữ liệu validation, định dạng JSON (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Tập dữ liệu validation, định dạng JSON (nếu có)',
    example: 'application/jsonl'
  })
  @IsString()
  @IsOptional()
  validDataset?: string;
}
