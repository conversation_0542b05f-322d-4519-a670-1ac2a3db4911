
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AgentStrategyAdminService } from '../services/agent-strategy.service';

@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/agent-strategies')
export class AgentStrategyController {
    constructor(private readonly agentStrategyService: AgentStrategyAdminService) { }
}