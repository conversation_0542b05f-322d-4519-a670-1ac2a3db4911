import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsArray, Transform, IsDateString, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tạo campaign từ template với format request tùy chỉnh
 */
export class CreateTemplateCampaignDto {
  /**
   * Tên campaign
   * @example "Xin chào"
   */
  @ApiProperty({
    description: 'Tên campaign',
    example: 'Xin chào',
  })
  @IsNotEmpty({ message: 'Tên campaign không được để trống' })
  @IsString({ message: 'Tên campaign phải là chuỗi' })
  name: string;

  /**
   * ID của template email
   * @example "36"
   */
  @ApiProperty({
    description: 'ID của template email',
    example: '36',
  })
  @IsNotEmpty({ message: 'Template ID không được để trống' })
  @IsString({ message: 'Template ID phải là chuỗi' })
  @Transform(({ value }) => String(value))
  templateId: string;

  /**
   * ID của email server configuration
   * @example "32"
   */
  @ApiProperty({
    description: 'ID của email server configuration',
    example: '32',
  })
  @IsNotEmpty({ message: 'Email Server ID không được để trống' })
  @IsString({ message: 'Email Server ID phải là chuỗi' })
  @Transform(({ value }) => String(value))
  emailServerId: string;

  /**
   * Danh sách ID của segments
   * @example ["17"]
   */
  @ApiProperty({
    description: 'Danh sách ID của segments',
    example: ['17'],
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Segment IDs phải là mảng' })
  @IsString({ each: true, message: 'Mỗi segment ID phải là chuỗi' })
  @Transform(({ value }) => Array.isArray(value) ? value.map(v => String(v)) : [])
  segmentIds?: string[];

  /**
   * Danh sách ID của audiences
   * @example []
   */
  @ApiProperty({
    description: 'Danh sách ID của audiences',
    example: [],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Audience IDs phải là mảng' })
  @IsString({ each: true, message: 'Mỗi audience ID phải là chuỗi' })
  @Transform(({ value }) => Array.isArray(value) ? value.map(v => String(v)) : [])
  audienceIds?: string[];

  /**
   * Thời gian dự kiến gửi (ISO string)
   * @example "2025-06-12T08:00:00.000Z"
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi (ISO string format)',
    example: '2025-06-12T08:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Thời gian gửi phải là định dạng ISO string hợp lệ' })
  scheduledAt?: string;

  /**
   * Mô tả campaign (tùy chọn)
   * @example "Campaign marketing cho khách hàng VIP"
   */
  @ApiProperty({
    description: 'Mô tả campaign',
    example: 'Campaign marketing cho khách hàng VIP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;
}

/**
 * DTO cho phản hồi tạo template campaign
 */
export class CreateTemplateCampaignResponseDto {
  /**
   * ID của campaign đã tạo
   * @example 123
   */
  @ApiProperty({
    description: 'ID của campaign đã tạo',
    example: 123,
  })
  campaignId: number;

  /**
   * Tên campaign
   * @example "Xin chào"
   */
  @ApiProperty({
    description: 'Tên campaign',
    example: 'Xin chào',
  })
  name: string;

  /**
   * Trạng thái campaign
   * @example "SCHEDULED"
   */
  @ApiProperty({
    description: 'Trạng thái campaign',
    example: 'SCHEDULED',
  })
  status: string;

  /**
   * Số lượng audience sẽ nhận campaign
   * @example 150
   */
  @ApiProperty({
    description: 'Số lượng audience sẽ nhận campaign',
    example: 150,
  })
  audienceCount: number;

  /**
   * Thời gian dự kiến gửi (Unix timestamp)
   * @example 1733990400
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi (Unix timestamp)',
    example: 1733990400,
    required: false,
  })
  scheduledAt?: number;

  /**
   * Thông tin template được sử dụng
   */
  @ApiProperty({
    description: 'Thông tin template được sử dụng',
    example: {
      id: 36,
      name: 'Welcome Email Template',
      subject: 'Chào mừng bạn đến với RedAI'
    },
  })
  template: {
    id: number;
    name: string;
    subject: string;
  };

  /**
   * Thông tin email server được sử dụng
   */
  @ApiProperty({
    description: 'Thông tin email server được sử dụng',
    example: {
      id: 32,
      serverName: 'Gmail SMTP Server',
      host: 'smtp.gmail.com'
    },
  })
  emailServer: {
    id: number;
    serverName: string;
    host: string;
  };
}
