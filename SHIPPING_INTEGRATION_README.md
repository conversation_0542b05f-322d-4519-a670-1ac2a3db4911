# 🚚 Tích hợp Vận chuyển - GHN & GHTK

## 📋 Tổng quan

Hệ thống đã được tích hợp với hai đơn vị vận chuyển chính tại Việt Nam:
- **GHN (Giao Hàng Nhanh)** - Ưu tiên cho các thành phố lớn
- **GHTK (Giao Hàng Tiết Kiệm)** - Ưu tiên cho các tỉnh thành

## 🔧 Cấu hình

### 1. Biến môi trường

Copy file `.env.shipping.example` thành `.env` và cập nhật các thông tin:

```bash
# GHN Configuration
GHN_TOKEN=your_ghn_token_here
GHN_SHOP_ID=your_ghn_shop_id_here

# GHTK Configuration  
GHTK_TOKEN=your_ghtk_token_here

# Shop Information - DEPRECATED
# Thông tin shop giờ được quản lý qua database
# Sử dụng API /v1/user/shop-info để tạo/cập nhật
```

### 2. Lấy Token và Shop ID

#### GHN:
1. Đăng ký tại: https://khachhang.giaohangnhanh.vn/
2. Vào **Cài đặt** → **Token** để lấy token
3. Vào **Cửa hàng** để lấy Shop ID

#### GHTK:
1. Đăng ký tại: https://khachhang.giaohangtietkiem.vn/
2. Vào **Cài đặt** → **API** để lấy token

## 🚀 Tính năng

### 1. Tự động tính phí vận chuyển
- Tính phí dựa trên địa chỉ giao hàng và thông tin sản phẩm
- Định tuyến tự động: GHN cho thành phố lớn, GHTK cho tỉnh
- Fallback: Nếu một đơn vị không khả dụng, tự động chuyển sang đơn vị khác

### 2. Tự động tạo vận đơn
- Submit đơn hàng đến đơn vị vận chuyển sau khi tạo thành công
- Lưu tracking number và thông tin vận đơn
- Cập nhật trạng thái đơn hàng

### 3. Tracking và cập nhật trạng thái
- API tracking để lấy trạng thái từ đơn vị vận chuyển
- Webhook để nhận cập nhật tự động
- Mapping trạng thái từ GHN/GHTK sang hệ thống

## 📡 API Endpoints

### 1. Quản lý thông tin shop
```http
GET /v1/user/shop-info          # Lấy thông tin shop
POST /v1/user/shop-info         # Tạo/cập nhật thông tin shop
PUT /v1/user/shop-info          # Cập nhật một phần thông tin shop
DELETE /v1/user/shop-info       # Xóa thông tin shop
GET /v1/user/shop-info/exists   # Kiểm tra shop info có tồn tại không
```

**Tạo/cập nhật thông tin shop:**
```json
{
  "shopName": "Cửa hàng ABC",
  "shopPhone": "0123456789",
  "shopAddress": "123 Đường ABC, Phường 1, Quận 1, TP.HCM",
  "shopProvince": "Hồ Chí Minh",
  "shopDistrict": "Quận 1",
  "shopWard": "Phường Bến Nghé"
}
```

### 2. Tính phí vận chuyển
```http
POST /v1/user/orders/calculate-shipping-fee
```

**Request:**
```json
{
  "products": [
    {
      "productId": 1,
      "quantity": 2
    }
  ],
  "deliveryAddress": "123 Đường ABC, Phường 1, Quận 1, TP.HCM",
  "preferredCarrier": "GHN"
}
```

**Lưu ý:**
- `preferredCarrier` là bắt buộc, người dùng phải chọn đơn vị vận chuyển (GHN hoặc GHTK)
- Trọng lượng và giá sản phẩm sẽ được lấy từ database dựa trên `productId`
- Kích thước sản phẩm sẽ được lấy từ `shipmentConfig` trong database

**Response:**
```json
{
  "success": true,
  "data": {
    "carrier": "GHN",
    "fee": 30000,
    "serviceType": "standard",
    "estimatedDeliveryTime": "2-3 ngày"
  }
}
```

### 3. Tracking đơn hàng
```http
GET /v1/user/orders/{id}/tracking
```

**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": 1,
    "trackingNumber": "GHN123456789",
    "carrier": "GHN",
    "status": {
      "status": "delivered",
      "description": "Đã giao hàng thành công"
    },
    "lastUpdated": 1641708800000
  }
}
```

### 4. Webhook endpoints
```http
POST /webhooks/shipping/ghn
POST /webhooks/shipping/ghtk
```

## 🔄 Luồng xử lý đơn hàng

### 1. Tạo đơn hàng
```
1. Validate thông tin sản phẩm và khách hàng
2. Tính phí vận chuyển và chọn đơn vị vận chuyển
3. Cập nhật tổng tiền đơn hàng (bao gồm phí ship)
4. Tạo đơn hàng trong database
5. Submit đơn hàng đến đơn vị vận chuyển
6. Lưu tracking number và thông tin vận đơn
```

### 2. Định tuyến đơn vị vận chuyển
```
- TP.HCM, Hà Nội, Đà Nẵng, Cần Thơ → GHN
- Các tỉnh khác → GHTK
- Nếu có chỉ định carrier ưu tiên → Sử dụng carrier đó
- Nếu carrier chính không khả dụng → Fallback sang carrier khác
```

### 3. Mapping trạng thái

#### GHN → Hệ thống:
- `ready_to_pick`, `picking` → `preparing`
- `picked`, `storing`, `transporting` → `in_transit`
- `sorting` → `sorting`
- `delivering` → `in_transit`
- `delivered` → `delivered`
- `delivery_fail` → `delivery_failed`
- `return` → `returning`
- `cancel` → `cancelled`

#### GHTK → Hệ thống:
- Status ID 1,2 → `preparing`
- Status ID 3,4 → `in_transit`
- Status ID 5 → `delivered`
- Status ID 6 → `delivery_failed`
- Status ID 7,8 → `returning`
- Status ID 9 → `cancelled`

## 🛠️ Cấu hình Webhook

### 1. Cấu hình trên GHN Dashboard
1. Vào **Cài đặt** → **Webhook**
2. Thêm URL: `https://yourdomain.com/webhooks/shipping/ghn`
3. Chọn các sự kiện cần nhận thông báo

### 2. Cấu hình trên GHTK Dashboard
1. Vào **Cài đặt** → **Webhook**
2. Thêm URL: `https://yourdomain.com/webhooks/shipping/ghtk`
3. Chọn các sự kiện cần nhận thông báo

## 🧪 Testing

### 1. Test Environment
```bash
# Sử dụng URL test
GHN_BASE_URL=https://dev-online-gateway.ghn.vn
GHTK_BASE_URL=https://services.ghtklab.com
```

### 2. Test Cases
- Tạo đơn hàng với địa chỉ TP.HCM (should use GHN)
- Tạo đơn hàng với địa chỉ tỉnh (should use GHTK)
- Test tracking với tracking number thực
- Test webhook với dữ liệu mẫu

## 🚨 Lưu ý quan trọng

1. **Token bảo mật**: Không commit token thực vào git
2. **Rate limiting**: GHN/GHTK có giới hạn số request/phút
3. **Timeout**: Cấu hình timeout phù hợp cho API calls
4. **Error handling**: Luôn có fallback khi API vận chuyển lỗi
5. **Logging**: Log tất cả các giao dịch với đơn vị vận chuyển

## 📞 Hỗ trợ

- **GHN**: https://khachhang.giaohangnhanh.vn/
- **GHTK**: https://khachhang.giaohangtietkiem.vn/
- **Documentation**: Xem thêm trong `/docs/shipping/`
