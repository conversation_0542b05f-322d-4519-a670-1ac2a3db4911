import { Column, Entity, PrimaryColumn, Unique } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_system trong cơ sở dữ liệu
 * Bảng lưu thông tin agent hệ thống, ví dụ: agent tự động hoặc mặc định của hệ thống
 */
@Entity('agents_system')
@Unique(['nameCode'])
export class AgentSystem {
  /**
   * UUID tham chiếu từ agents.id
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * ID nhân viên tạo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID nhân viên cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID nhân viên xóa
   */
  @Column({ name: 'deleted_by', type: 'integer', nullable: true })
  deletedBy: number | null;

  /**
   * Tên định danh
   */
  @Column({
    name: 'name_code',
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Tên định danh'
  })
  nameCode: string;

  /**
   * Mô tả về agent system
   */
  @Column({
    type: 'text',
    nullable: true,
    comment: 'Mô tả về agent system'
  })
  description?: string;

  /**
   * Có phải supervisor không
   */
  @Column({
    name: 'is_supervisor',
    type: 'boolean',
    default: false,
    comment: 'Có phải supervisor không'
  })
  isSupervisor: boolean;

  /**
   * Trạng thái hoạt động
   */
  @Column({
    name: 'active',
    type: 'boolean',
    default: false,
    comment: 'Trạng thái hoạt động'
  })
  active: boolean;

  /**
   * UUID tham chiếu đến bảng system_models
   */
  @Column({
    name: 'system_model_id',
    type: 'uuid',
    comment: 'UUID tham chiếu đến bảng system_models'
  })
  systemModelId: string;
}
