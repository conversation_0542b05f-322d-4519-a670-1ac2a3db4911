import { Injectable, Logger } from '@nestjs/common';
import { ModelRegistryRepository } from '../../repositories/model-registry.repository';

/**
 * Service xử lý business logic cho Admin Model Registry
 */
@Injectable()
export class AdminModelRegistryService {
  private readonly logger = new Logger(AdminModelRegistryService.name);

  constructor(
    private readonly modelRegistryRepository: ModelRegistryRepository,
  ) { }
}
