/**
 * Enum định nghĩa các loại sản phẩm
 */
export enum ProductTypeEnum {
  /**
   * Sản phẩm vật lý - sản phẩm có thể chạm được, cần vận chuyển
   */
  PHYSICAL = 'PHYSICAL',

  /**
   * Sản phẩm số - sản phẩm kỹ thuật số, không cần vận chuyển
   */
  DIGITAL = 'DIGITAL',

  /**
   * Sự kiện - vé tham gia sự kiện, hộ<PERSON> thảo, khóa học
   */
  EVENT = 'EVENT',

  /**
   * Dịch vụ - cung cấp dịch vụ, tư vấn
   */
  SERVICE = 'SERVICE',

  /**
   * Combo - gói sản phẩm kết hợp nhiều loại sản phẩm
   */
  COMBO = 'COMBO',
}

/**
 * <PERSON><PERSON> tả loại sản phẩm bằng tiếng Việt
 */
export const PRODUCT_TYPE_DESCRIPTIONS = {
  [ProductTypeEnum.PHYSICAL]: 'Sản phẩm vật lý',
  [ProductTypeEnum.DIGITAL]: 'Sản phẩm số',
  [ProductTypeEnum.EVENT]: 'Sự kiện',
  [ProductTypeEnum.SERVICE]: 'Dịch vụ',
  [ProductTypeEnum.COMBO]: 'Combo sản phẩm'
};
