# Agent Block Processing Implementation

## Project Overview
Implement các block processing methods trong Agent User Service để xử lý các khối cấu hình agent theo TypeAgent config. Các methods này sẽ được gọi khi tạo agent mới với cấu trúc modular.

## Core Requirements

### 1. Conversion Block Processing
- **Mục tiêu**: <PERSON><PERSON><PERSON> c<PERSON> hình conversion vào field `convertConfig` trong `agents_user.entity.ts`
- **Input**: `CreateConvertDto[]` - mảng các field conversion config
- **Output**: Cập nhật convertConfig field trong agents_user table
- **Validation**: Validate schema conversion fields (name, type, content, required)

### 2. Output Messenger Block Processing  
- **Mục tiêu**: Liên kết Facebook Pages với Agent và subscribe app
- **Input**: `OutputMessengerBlockDto` với facebookPageIds
- **Output**: <PERSON>ập nhật agent_id trong facebook_page table và subscribe webhook
- **Integration**: Sử dụng FacebookService.subscribeApp() cho pages chưa có agent
- **Validation**: Kiểm tra ownership và existence của Facebook pages

### 3. Output Website Block Processing
- **Mục tiêu**: Liên kết User Websites với Agent  
- **Input**: `OutputWebsiteBlockDto` với userWebsiteIds
- **Output**: Cập nhật agent_id trong user_websites table
- **Validation**: Kiểm tra ownership và existence của websites

### 4. Resources Block Processing
- **Mục tiêu**: Liên kết URLs, Media, Products với Agent
- **Input**: ResourcesBlockDto với urlIds, mediaIds, productIds
- **Output**: Tạo records trong agents_url, agents_media, agents_product tables
- **Repositories**: Sử dụng AgentUrlRepository, AgentMediaRepository, AgentProductRepository
- **Validation**: Kiểm tra ownership và existence của resources

### 5. Strategy Block Processing
- **Mục tiêu**: Liên kết Strategy với Agent
- **Input**: StrategyBlockDto với strategyId
- **Output**: Tạo/cập nhật record trong agents_strategy_user table
- **Validation**: Kiểm tra strategy ownership và existence

## Technical Requirements

### Database Schema Updates
- Thêm convertConfig field (JSONB) vào agents_user entity
- Sử dụng existing junction tables cho resources
- Cập nhật agent_id trong facebook_page và user_websites

### Error Handling
- Sử dụng AppException với AGENT_ERROR_CODES
- Validate ownership trước khi thao tác
- Rollback transactions nếu có lỗi

### Performance Considerations
- Batch operations cho multiple resources
- Validate existence trước khi insert
- Sử dụng transactions cho data consistency

### Integration Points
- FacebookService cho webhook subscription
- Existing repositories cho resource management
- TypeAgent validation helper

## Success Criteria
- Tất cả block processing methods hoạt động chính xác
- Data integrity được đảm bảo
- Error handling đầy đủ
- Performance tối ưu cho batch operations
- Integration tests pass

## Dependencies
- AgentUserService đã có sẵn
- Repositories cho resources đã implement
- FacebookService có subscribeApp method
- DTOs cho các blocks đã định nghĩa
