import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToClass } from 'class-transformer';
import * as crypto from 'crypto';
import { UserProviderShipmentRepository } from '@modules/integration/repositories';
import { UserProviderShipment } from '@modules/integration/entities';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';
import {
  CreateUserProviderShipmentDto,
  UpdateUserProviderShipmentDto,
  UserProviderShipmentResponseDto,
  GHTKConfigResponseDto,
  GHNConfigResponseDto
} from '../dto/user-provider-shipment';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { PaginatedResult } from '@common/response';
import { GHTK_BASE_URLS } from '@modules/business/constants';
import { GHN_BASE_URLS } from '@modules/business/constants';

/**
 * Service xử lý cấu hình provider shipment của user
 */
@Injectable()
export class UserProviderShipmentService {
  private readonly logger = new Logger(UserProviderShipmentService.name);
  private readonly encryptionKey: string;

  constructor(
    private readonly repository: UserProviderShipmentRepository,
    private readonly configService: ConfigService
  ) {
    // Không cần ENCRYPTION_KEY chung nữa, sử dụng key riêng cho từng provider
    this.encryptionKey = 'fallback-encryption-key-32-chars!!';
  }

  /**
   * Lấy encryption key theo provider type
   */
  private getEncryptionKey(type: ProviderShipmentType): string {
    switch (type) {
      case ProviderShipmentType.GHTK:
        return this.configService.get<string>('GHTK_ENCRYPTION_KEY') || this.encryptionKey;
      case ProviderShipmentType.GHN:
        return this.configService.get<string>('GHN_ENCRYPTION_KEY') || this.encryptionKey;
      default:
        return this.encryptionKey;
    }
  }

  /**
   * Mã hóa dữ liệu cấu hình với key riêng cho từng provider
   */
  private encrypt(text: string, type: ProviderShipmentType): string {
    try {
      const algorithm = 'aes-256-cbc';
      const encryptionKey = this.getEncryptionKey(type);
      const key = crypto.scryptSync(encryptionKey, 'salt', 32);
      const iv = crypto.randomBytes(16);

      const cipher = crypto.createCipheriv(algorithm, key, iv);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error('Lỗi khi mã hóa dữ liệu:', error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi mã hóa dữ liệu');
    }
  }

  /**
   * Giải mã dữ liệu cấu hình với key riêng cho từng provider
   */
  private decrypt(encryptedText: string, type: ProviderShipmentType): string {
    try {
      const algorithm = 'aes-256-cbc';
      const encryptionKey = this.getEncryptionKey(type);
      const key = crypto.scryptSync(encryptionKey, 'salt', 32);

      const textParts = encryptedText.split(':');
      const iv = Buffer.from(textParts.shift()!, 'hex');
      const encryptedData = textParts.join(':');

      const decipher = crypto.createDecipheriv(algorithm, key, iv);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Lỗi khi giải mã dữ liệu:', error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi giải mã dữ liệu');
    }
  }

  /**
   * Tạo cấu hình mới
   */
  async create(userId: number, dto: CreateUserProviderShipmentDto): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Tạo cấu hình ${dto.type} cho user ${userId}`);

      // Validate cấu hình theo type
      this.validateConfigByType(dto);

      // Tạo object cấu hình JSON để mã hóa
      let configJson: any = {};

      if (dto.type === ProviderShipmentType.GHTK && dto.ghtkConfig) {
        const isTestMode = dto.ghtkConfig.isTestMode ?? true;
        configJson = {
          token: dto.ghtkConfig.token,
          baseUrl: this.getBaseUrlByMode(dto.type, isTestMode),
          timeout: dto.ghtkConfig.timeout || 30000,
          isTestMode: isTestMode
        };
      }

      if (dto.type === ProviderShipmentType.GHN && dto.ghnConfig) {
        const isTestMode = dto.ghnConfig.isTestMode ?? true;
        configJson = {
          token: dto.ghnConfig.token,
          shopId: dto.ghnConfig.shopId,
          baseUrl: this.getBaseUrlByMode(dto.type, isTestMode),
          timeout: dto.ghnConfig.timeout || 30000,
          isTestMode: isTestMode
        };
      }

      // Mã hóa cấu hình JSON
      const encryptedKey = this.encrypt(JSON.stringify(configJson), dto.type);

      // Tạo entity
      const newEntity = this.repository.create({
        userId,
        name: dto.name || `Cấu hình ${dto.type}`,
        type: dto.type,
        key: encryptedKey
      });

      const entity = await this.repository.save(newEntity);

      return this.transformToResponse(entity);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi tạo cấu hình: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách cấu hình với phân trang
   */
  async findByUserId(
    userId: number,
    page: number = 1,
    limit: number = 10,
    type?: ProviderShipmentType
  ): Promise<PaginatedResult<UserProviderShipmentResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách cấu hình cho user ${userId}`);

      const result = await this.repository.findByUserIdWithPagination(userId, page, limit, type);

      return {
        items: result.items.map(item => this.transformToResponse(item)),
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấu hình:`, error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi lấy danh sách cấu hình: ${error.message}`);
    }
  }

  /**
   * Lấy cấu hình theo ID
   */
  async findById(userId: number, id: string): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Lấy cấu hình ${id} cho user ${userId}`);

      const entity = await this.repository.findByIdAndUserId(id, userId);
      if (!entity) {
        throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy cấu hình');
      }

      return this.transformToResponse(entity);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi lấy cấu hình: ${error.message}`);
    }
  }

  /**
   * Cập nhật cấu hình
   */
  async update(
    userId: number, 
    id: string, 
    dto: UpdateUserProviderShipmentDto
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`Cập nhật cấu hình ${id} cho user ${userId}`);

      const entity = await this.repository.findByIdAndUserId(id, userId);
      if (!entity) {
        throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy cấu hình');
      }

      // Giải mã cấu hình hiện tại
      const currentConfig = JSON.parse(this.decrypt(entity.key, entity.type));

      // Cập nhật cấu hình
      if (dto.ghtkConfig && entity.type === ProviderShipmentType.GHTK) {
        const newIsTestMode = dto.ghtkConfig.isTestMode ?? currentConfig.isTestMode;
        Object.assign(currentConfig, {
          token: dto.ghtkConfig.token || currentConfig.token,
          baseUrl: this.getBaseUrlByMode(entity.type, newIsTestMode),
          timeout: dto.ghtkConfig.timeout || currentConfig.timeout,
          isTestMode: newIsTestMode
        });
      }

      if (dto.ghnConfig && entity.type === ProviderShipmentType.GHN) {
        const newIsTestMode = dto.ghnConfig.isTestMode ?? currentConfig.isTestMode;
        Object.assign(currentConfig, {
          token: dto.ghnConfig.token || currentConfig.token,
          shopId: dto.ghnConfig.shopId || currentConfig.shopId,
          baseUrl: this.getBaseUrlByMode(entity.type, newIsTestMode),
          timeout: dto.ghnConfig.timeout || currentConfig.timeout,
          isTestMode: newIsTestMode
        });
      }

      // Mã hóa lại
      const encryptedKey = this.encrypt(JSON.stringify(currentConfig), entity.type);

      // Cập nhật entity
      const updateData: Partial<UserProviderShipment> = {
        key: encryptedKey
      };

      if (dto.name) {
        updateData.name = dto.name;
      }

      // Cập nhật entity
      Object.assign(entity, updateData);
      await this.repository.save(entity);

      // Lấy entity đã cập nhật
      const updatedEntity = await this.repository.findByIdAndUserId(id, userId);
      return this.transformToResponse(updatedEntity!);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi cập nhật cấu hình: ${error.message}`);
    }
  }

  /**
   * Xóa cấu hình
   */
  async delete(userId: number, id: string): Promise<void> {
    try {
      this.logger.log(`Xóa cấu hình ${id} cho user ${userId}`);

      const exists = await this.repository.findByIdAndUserId(id, userId);
      if (!exists) {
        throw new AppException(ErrorCode.NOT_FOUND, 'Không tìm thấy cấu hình');
      }

      const deleted = await this.repository.deleteByIdAndUserId(id, userId);
      if (!deleted) {
        throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xóa cấu hình');
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa cấu hình:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi xóa cấu hình: ${error.message}`);
    }
  }

  /**
   * Validate cấu hình theo type
   */
  private validateConfigByType(dto: CreateUserProviderShipmentDto): void {
    if (dto.type === ProviderShipmentType.GHTK && !dto.ghtkConfig) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Cấu hình GHTK là bắt buộc khi type = GHTK');
    }

    if (dto.type === ProviderShipmentType.GHN && !dto.ghnConfig) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Cấu hình GHN là bắt buộc khi type = GHN');
    }

    if (dto.type === ProviderShipmentType.GHTK && dto.ghnConfig) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Không thể có cấu hình GHN khi type = GHTK');
    }

    if (dto.type === ProviderShipmentType.GHN && dto.ghtkConfig) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Không thể có cấu hình GHTK khi type = GHN');
    }
  }

  /**
   * Transform entity thành response DTO
   */
  private transformToResponse(entity: UserProviderShipment): UserProviderShipmentResponseDto {
    try {
      const config = JSON.parse(this.decrypt(entity.key, entity.type));

      const response = plainToClass(UserProviderShipmentResponseDto, {
        id: entity.id,
        name: entity.name,
        type: entity.type,
        createdAt: entity.createdAt
      });

      // Thêm cấu hình response (bao gồm thông tin đã giải mã)
      if (entity.type === ProviderShipmentType.GHTK) {
        response.ghtkConfig = plainToClass(GHTKConfigResponseDto, {
          baseUrl: config.baseUrl,
          timeout: config.timeout,
          isTestMode: config.isTestMode,
          token: config.token,
          hasToken: !!config.token
        });
      }

      if (entity.type === ProviderShipmentType.GHN) {
        response.ghnConfig = plainToClass(GHNConfigResponseDto, {
          baseUrl: config.baseUrl,
          timeout: config.timeout,
          isTestMode: config.isTestMode,
          token: config.token,
          shopId: config.shopId,
          hasToken: !!config.token,
          hasShopId: !!config.shopId
        });
      }

      return response;
    } catch (error) {
      this.logger.error('Lỗi khi transform response:', error);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi xử lý dữ liệu cấu hình');
    }
  }

  /**
   * Lấy cấu hình đã giải mã cho internal use
   */
  async getDecryptedConfig(userId: number, type: ProviderShipmentType): Promise<any | null> {
    try {
      const entities = await this.repository.findByTypeAndUserId(type, userId);
      if (entities.length === 0) {
        return null;
      }

      // Lấy cấu hình đầu tiên (mới nhất)
      const entity = entities[0];
      const config = JSON.parse(this.decrypt(entity.key, entity.type));

      // Trả về config với format chuẩn cho business module
      return {
        ...config,
        type: entity.type,
        name: entity.name
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình giải mã:`, error);
      return null;
    }
  }

  /**
   * Lấy BASE_URL dựa trên provider type và test mode
   */
  private getBaseUrlByMode(type: ProviderShipmentType, isTestMode: boolean): string {
    switch (type) {
      case ProviderShipmentType.GHTK:
        return isTestMode ? GHTK_BASE_URLS.TEST : GHTK_BASE_URLS.PRODUCTION;

      case ProviderShipmentType.GHN:
        return isTestMode ? GHN_BASE_URLS.TEST : GHN_BASE_URLS.PRODUCTION;

      default:
        throw new AppException(ErrorCode.VALIDATION_ERROR, `Unsupported provider type: ${type}`);
    }
  }
}
