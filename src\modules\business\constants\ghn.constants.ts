/**
 * Constants cho GHN API (Giao Hàng Nhanh)
 */

/**
 * Base URLs cho GHN API
 */
export const GHN_BASE_URLS = {
  TEST: 'https://dev-online-gateway.ghn.vn',
  PRODUCTION: 'https://online-gateway.ghn.vn'
} as const;

/**
 * GHN API Endpoints
 */
export const GHN_ENDPOINTS = {
  // Đơn hàng (Order)
  CREATE_ORDER: '/shiip/public-api/v2/shipping-order/create',
  UPDATE_ORDER: '/shiip/public-api/v2/shipping-order/update',
  CANCEL_ORDER: '/shiip/public-api/v2/switch-status/cancel',
  RETURN_ORDER: '/shiip/public-api/v2/switch-status/return',
  DELIVERY_AGAIN: '/shiip/public-api/v2/switch-status/storing',
  ORDER_INFO: '/shiip/public-api/v2/shipping-order/detail',
  ORDER_INFO_BY_CLIENT_CODE: '/shiip/public-api/v2/shipping-order/detail-by-client-code',
  PREVIEW_ORDER: '/shiip/public-api/v2/shipping-order/preview',
  UPDATE_COD: '/shiip/public-api/v2/shipping-order/updateCOD',
  PRINT_ORDER: '/shiip/public-api/v2/a5/gen-token',
  PRINT_A5: '/a5/public-api/printA5',
  PRINT_80x80: '/a5/public-api/print80x80',
  PRINT_52x70: '/a5/public-api/print52x70',
  
  // Tính phí (Calculate Fee)
  CALCULATE_FEE: '/shiip/public-api/v2/shipping-order/fee',
  ORDER_FEE: '/shiip/public-api/v2/shipping-order/soc',
  GET_SERVICE: '/shiip/public-api/v2/shipping-order/available-services',
  LEADTIME: '/shiip/public-api/v2/shipping-order/leadtime',
  
  // Cửa hàng (Store)
  GET_STORES: '/shiip/public-api/v2/shop/all',
  CREATE_STORE: '/shiip/public-api/v2/shop/register',
  
  // Địa chỉ (Address)
  GET_PROVINCES: '/public-api/master-data/province',
  GET_DISTRICTS: '/public-api/master-data/district',
  GET_WARDS: '/public-api/master-data/ward',
  GET_STATIONS: '/shiip/public-api/v2/station/get',
  
  // Ca lấy hàng (Pick Shift)
  GET_PICK_SHIFTS: '/shiip/public-api/v2/shift/date',
  
  // Ticket (Hỗ trợ)
  CREATE_TICKET: '/public-api/ticket/create',
  REPLY_TICKET: '/public-api/ticket/reply',
  GET_TICKETS: '/public-api/ticket/index',
  
  // Đối tác (Affiliate)
  GET_OTP: '/shiip/public-api/v2/shop/affiliateOTP',
  CREATE_STORE_BY_OTP: '/shiip/public-api/v2/shop/affiliateCreate',
  ADD_STAFF_BY_OTP: '/shiip/public-api/v2/shop/affiliateCreateWithShop'
} as const;

/**
 * GHN Default Headers
 */
export const GHN_DEFAULT_HEADERS = {
  'Content-Type': 'application/json'
} as const;

/**
 * GHN Test Configuration
 * Sử dụng token test từ tài liệu GHN
 * IMPORTANT: Thay thế bằng token và shop ID thực từ GHN
 */
export const GHN_TEST_CONFIG = {
  TOKEN: 'your_real_ghn_token_here',
  SHOP_ID: 'your_real_shop_id_here',
  BASE_URL: GHN_BASE_URLS.TEST
} as const;

/**
 * GHN Service Types
 */
export const GHN_SERVICE_TYPES = {
  E_COMMERCE: 2,
  TRADITIONAL: 5
} as const;

/**
 * GHN Payment Types
 */
export const GHN_PAYMENT_TYPES = {
  SENDER_PAYS: 1,
  RECEIVER_PAYS: 2
} as const;

/**
 * GHN Required Notes
 */
export const GHN_REQUIRED_NOTES = {
  ALLOW_CHECKING: 'CHOTHUHANG',
  ALLOW_CHECKING_NO_PAYMENT: 'CHOXEMHANGKHONGTHU',
  NO_CHECKING: 'KHONGCHOXEMHANG'
} as const;

/**
 * GHN Order Status
 */
export const GHN_ORDER_STATUS = {
  READY_TO_PICK: 'ready_to_pick',
  PICKING: 'picking',
  CANCEL: 'cancel',
  MONEY_COLLECT_PICKING: 'money_collect_picking',
  PICKED: 'picked',
  STORING: 'storing',
  TRANSPORTING: 'transporting',
  SORTING: 'sorting',
  DELIVERING: 'delivering',
  MONEY_COLLECT_DELIVERING: 'money_collect_delivering',
  DELIVERED: 'delivered',
  DELIVERY_FAIL: 'delivery_fail',
  WAITING_TO_RETURN: 'waiting_to_return',
  RETURN: 'return'
} as const;

/**
 * GHN Ticket Categories
 */
export const GHN_TICKET_CATEGORIES = {
  CONSULTATION: 'Tư vấn',
  DELIVERY_REQUEST: 'Hối Giao/Lấy/Trả hàng',
  CHANGE_INFO: 'Thay đổi thông tin',
  COMPLAINT: 'Khiếu nại'
} as const;

/**
 * GHN Ticket Status
 */
export const GHN_TICKET_STATUS = {
  PROCESSING: 1,
  WAITING_CUSTOMER: 2,
  COMPLETED: 3
} as const;

/**
 * GHN Error Messages
 */
export const GHN_ERROR_MESSAGES = {
  INVALID_TOKEN: 'Token GHN không hợp lệ',
  INVALID_SHOP_ID: 'Shop ID GHN không hợp lệ',
  API_ERROR: 'Lỗi API GHN',
  NETWORK_ERROR: 'Lỗi kết nối mạng với GHN',
  INVALID_CONFIG: 'Cấu hình GHN không hợp lệ',
  ORDER_NOT_FOUND: 'Không tìm thấy đơn hàng',
  INVALID_ADDRESS: 'Địa chỉ không hợp lệ',
  INVALID_SERVICE: 'Dịch vụ không hợp lệ',
  WEIGHT_EXCEEDED: 'Khối lượng vượt quá giới hạn',
  DIMENSION_EXCEEDED: 'Kích thước vượt quá giới hạn',
  COD_EXCEEDED: 'Số tiền COD vượt quá giới hạn',
  INSURANCE_EXCEEDED: 'Giá trị bảo hiểm vượt quá giới hạn'
} as const;

/**
 * GHN Timeout Configuration
 */
export const GHN_TIMEOUT = 10000; // 10 seconds - tối ưu hóa tốc độ

/**
 * GHN Limits
 */
export const GHN_LIMITS = {
  MAX_WEIGHT: 50000, // 50kg in grams
  MAX_DIMENSION: 200, // 200cm
  MAX_COD: 50000000, // 50 million VND
  MAX_INSURANCE: 5000000, // 5 million VND
  MAX_CONTENT_LENGTH: 2000 // 2000 characters
} as const;

/**
 * GHN Print Formats
 */
export const GHN_PRINT_FORMATS = {
  A5: 'A5',
  THERMAL_80x80: '80x80',
  THERMAL_52x70: '52x70'
} as const;

/**
 * GHN Print URLs by Format
 */
export const GHN_PRINT_URLS = {
  A5: '/a5/public-api/printA5',
  THERMAL_80x80: '/a5/public-api/print80x80',
  THERMAL_52x70: '/a5/public-api/print52x70'
} as const;

/**
 * GHN Webhook Types
 */
export const GHN_WEBHOOK_TYPES = {
  CREATE: 'create',
  SWITCH_STATUS: 'switch_status',
  UPDATE_WEIGHT: 'update_weight',
  UPDATE_COD: 'update_cod',
  UPDATE_FEE: 'update_fee'
} as const;
