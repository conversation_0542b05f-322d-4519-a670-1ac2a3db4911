import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@modules/user/entities/user.entity';
import { KnowledgeFile } from '@modules/data/knowledge-files/entities/knowledge-file.entity';
import { Media } from '@modules/data/media/entities/media.entity';
import { Url } from '@modules/data/url/entities/url.entity';
import { VectorStore } from '@modules/data/knowledge-files/entities/vector-store.entity';
import { StatisticsController } from './controllers/statistics.controller';
import { StatisticsService } from './services/statistics.service';
import { MediaRepository } from '@modules/data/media/repositories';
import { KnowledgeFileRepository, VectorStoreRepository } from '@modules/data/knowledge-files/repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, KnowledgeFile, Media, Url, VectorStore]),
  ],
  controllers: [StatisticsController],
  providers: [
    StatisticsService,
    MediaRepository,
    KnowledgeFileRepository,
    VectorStoreRepository,
  ],
  exports: [StatisticsService],
})
export class StatisticsModule {}
