import { Body, Controller, Get, HttpCode, HttpStatus, Logger, Param, ParseIntPipe, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserOrderService } from '../services/user-order.service';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { QueryUserOrderDto, UserOrderListItemDto, UserOrderResponseDto, UserOrderStatusResponseDto, CreateUserOrderDto } from '../dto';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * Controller xử lý các request liên quan đến đơn hàng của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_ORDER)
@Controller('user/orders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, UserOrderResponseDto, UserOrderListItemDto, UserOrderStatusResponseDto, CreateUserOrderDto, PaginatedResult)
export class UserOrderController {
  private readonly logger = new Logger(UserOrderController.name);

  constructor(private readonly userOrderService: UserOrderService) {}

  /**
   * Tạo đơn hàng mới
   * @param createOrderDto DTO chứa thông tin đơn hàng
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin đơn hàng đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiBody({
    type: CreateUserOrderDto,
    examples: {
      'toi-thieu-ghn': {
        summary: '✅ Tối thiểu - GHN',
        description: 'Request ngắn gọn với các trường cần thiết sử dụng Giao Hàng Nhanh (GHN)',
        value: {
          shopId: 1,
          customerInfo: { customerId: 19 },
          products: [{ productId: 60, quantity: 2 }],
          billInfo: {
            subtotal: 300000,
            shippingFee: 31500,
            selectedCarrier: "GHN",
            shippingServiceType: "standard",
            discount: 10000,
            paymentMethod: "CASH"
          },
          logisticInfo: {
            shippingMethod: "Chuẩn",
            carrier: "GHN",
            shippingNote: "Giao hàng trong giờ hành chính",
            deliveryAddress: {
              addressId: 1
            }
          },
          source: "website",
          note: "Đơn hàng ưu tiên - GHN",
          tags: ["urgent", "ghn"]
        }
      },
      'toi-thieu-ghtk': {
        summary: '✅ Tối thiểu - GHTK',
        description: 'Request ngắn gọn với các trường cần thiết sử dụng Giao Hàng Tiết Kiệm (GHTK)',
        value: {
          shopId: 1,
          customerInfo: { customerId: 19 },
          products: [{ productId: 60, quantity: 2 }],
          billInfo: {
            subtotal: 300000,
            shippingFee: 21500,
            selectedCarrier: "GHTK",
            shippingServiceType: "standard",
            discount: 10000,
            paymentMethod: "CASH"
          },
          logisticInfo: {
            shippingMethod: "Đường bộ",
            carrier: "GHTK",
            shippingNote: "Giao hàng trong giờ hành chính",
            deliveryAddress: {
              addressId: 1
            }
          },
          source: "website",
          note: "Đơn hàng ưu tiên - GHTK",
          tags: ["urgent", "ghtk"]
        }
      }
    }
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
    BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
    BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND
  )
  async createOrder(
    @Body() createOrderDto: CreateUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      this.logger.log(`Tạo đơn hàng mới cho userId=${user.id}`);
      const order = await this.userOrderService.createOrder(user.id, createOrderDto);
      return ApiResponseDto.success(order, 'Tạo đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách đơn hàng của người dùng
   * @param queryDto DTO chứa các tham số truy vấn
   * @param user Thông tin người dùng từ JWT
   * @returns Danh sách đơn hàng với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách đơn hàng',
    description: 'Lấy danh sách đơn hàng của người dùng với thông tin khách hàng chuyển đổi đầy đủ (thay vì chỉ userConvertCustomerId)'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách đơn hàng với thông tin khách hàng đầy đủ',
    schema: ApiResponseDto.getPaginatedSchema(UserOrderListItemDto),
  })
  @ApiErrorResponse(BUSINESS_ERROR_CODES.ORDER_FIND_FAILED)
  async getOrders(
    @Query() queryDto: QueryUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<UserOrderListItemDto>>> {
    try {
      this.logger.log(`Lấy danh sách đơn hàng cho userId=${user.id}`);
      const orders = await this.userOrderService.findAll(user.id, queryDto);
      return ApiResponseDto.success(orders, 'Lấy danh sách đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID của đơn hàng
   * @param user Thông tin người dùng từ JWT
   * @returns Chi tiết đơn hàng
   */
  @Get('detail/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy chi tiết đơn hàng' })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết đơn hàng',
    schema: ApiResponseDto.getSchema(UserOrderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED
  )
  async getOrderDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      this.logger.log(`Lấy chi tiết đơn hàng id=${id} cho userId=${user.id}`);
      const order = await this.userOrderService.findById(id, user.id);
      return ApiResponseDto.success(order, 'Lấy chi tiết đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng và vận chuyển của người dùng
   */
  @Get('status-stats')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy thống kê trạng thái đơn hàng và vận chuyển',
    description: 'Lấy thống kê số lượng đơn hàng theo trạng thái đơn hàng và trạng thái vận chuyển của người dùng hiện tại',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thống kê thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/UserOrderStatusResponseDto' },
          },
        },
      ],
    },
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
  )
  async getOrderStatusStats(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderStatusResponseDto>> {
    try {
      this.logger.log(`Lấy thống kê trạng thái đơn hàng cho userId=${user.id}`);
      const stats = await this.userOrderService.getOrderStatusStats(user.id);
      return ApiResponseDto.success(stats, 'Lấy thống kê trạng thái đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }
}
