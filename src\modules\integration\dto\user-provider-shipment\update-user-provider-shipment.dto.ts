import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho cập nhật cấu hình <PERSON>HTK (partial)
 */
export class UpdateGHTKConfigDto {
  @ApiProperty({
    description: 'GHTK API Token',
    example: '8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou',
    required: false
  })
  @IsOptional()
  @IsString()
  token?: string;



  @ApiProperty({
    description: 'GHTK Timeout (milliseconds)',
    example: 30000,
    required: false
  })
  @IsOptional()
  timeout?: number;

  @ApiProperty({
    description: 'GHTK Environment Mode (true = test/staging, false = production)',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isTestMode?: boolean;
}

/**
 * DTO cho cập nhật cấu hình <PERSON> (partial)
 */
export class UpdateGHNConfigDto {
  @ApiProperty({
    description: 'GHN API Token',
    example: '42d0fc57-402d-11f0-9b81-222185cb68c8',
    required: false
  })
  @IsOptional()
  @IsString()
  token?: string;

  @ApiProperty({
    description: 'GHN Shop ID',
    example: '196768',
    required: false
  })
  @IsOptional()
  @IsString()
  shopId?: string;



  @ApiProperty({
    description: 'GHN Timeout (milliseconds)',
    example: 30000,
    required: false
  })
  @IsOptional()
  timeout?: number;

  @ApiProperty({
    description: 'GHN Environment Mode (true = test/dev, false = production)',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isTestMode?: boolean;
}

/**
 * DTO cho cập nhật cấu hình provider shipment
 */
export class UpdateUserProviderShipmentDto {
  @ApiProperty({
    description: 'Tên hiển thị của cấu hình',
    example: 'Cấu hình GHTK chính - Updated',
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Cấu hình GHTK (chỉ khi type = GHTK)',
    type: UpdateGHTKConfigDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateGHTKConfigDto)
  ghtkConfig?: UpdateGHTKConfigDto;

  @ApiProperty({
    description: 'Cấu hình GHN (chỉ khi type = GHN)',
    type: UpdateGHNConfigDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateGHNConfigDto)
  ghnConfig?: UpdateGHNConfigDto;
}
