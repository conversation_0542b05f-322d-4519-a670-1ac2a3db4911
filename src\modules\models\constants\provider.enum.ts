import { AppException, ErrorCode } from "@/common";

export enum ProviderEnum {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
  META = 'META',
  DEEPSEEK = 'DEEPSEEK',
  XAI = 'XAI',
}

export enum ProviderFineTuneEnum {
  OPENAI = 'OPENAI',
  GOOGLE = 'GOOGLE',
}

/**
 * Object tiện ích để làm việc với ProviderEnum
 */
export const ProviderUtil = {
  /**
   * Lấy giá trị chuỗi của một loại file
   * @param type Loại file
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: ProviderEnum): string {
    return type;
  },

  /**
   * Lấy enum FileTypeEnum từ tên loại file hoặc giá trị MIME type
   * @param type Tên loại file (key của enum) hoặc giá trị MIME type (ví dụ: 'application/pdf')
   * @returns Giá trị enum FileTypeEnum tương ứng
   * @throws AppException nếu loại file không tồn tại
   */
  getMimeType(type: string): ProviderEnum {
    // Kiểm tra nếu là key của enum (ví dụ: 'PDF')
    const typeFromKey = ProviderEnum[type as keyof typeof ProviderEnum];
    if (typeFromKey) {
      return typeFromKey;
    }

    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'application/pdf')
    const entries = Object.entries(ProviderEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return ProviderEnum[entry[0] as keyof typeof ProviderEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new AppException(
      ErrorCode.NOT_FOUND,
      `Loại nhà cung cấp AI '${type}' không được hỗ trợ`
    );
  },
};


/**
 * Object tiện ích để làm việc với ProviderFineTuneEnum
 */
export const ProviderFineTuneUtil = {
  /**
   * Lấy giá trị chuỗi của một loại file
   * @param type Loại file
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: ProviderFineTuneEnum): string {
    return type;
  },

  /**
   * Lấy enum FileTypeEnum từ tên loại file hoặc giá trị MIME type
   * @param type Tên loại file (key của enum) hoặc giá trị MIME type (ví dụ: 'application/pdf')
   * @returns Giá trị enum FileTypeEnum tương ứng
   * @throws AppException nếu loại file không tồn tại
   */
  getMimeType(type: string): ProviderFineTuneEnum {
    // Kiểm tra nếu là key của enum (ví dụ: 'PDF')
    const typeFromKey = ProviderFineTuneEnum[type as keyof typeof ProviderFineTuneEnum];
    if (typeFromKey) {
      return typeFromKey;
    }

    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'application/pdf')
    const entries = Object.entries(ProviderFineTuneEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return ProviderFineTuneEnum[entry[0] as keyof typeof ProviderFineTuneEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new AppException(
      ErrorCode.NOT_FOUND,
      `Loại nhà cung cấp AI '${type}' không được hỗ trợ`
    );
  },
};