  import { ApiProperty } from '@nestjs/swagger';
  import { Type } from 'class-transformer';
  import {
    IsArray,
    IsEnum,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
    MaxLength,
    ValidateNested,
  } from 'class-validator';
  import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
  import { BusinessShipmentConfigDto, HasPriceDto, StringPriceDto } from './create-product.dto';
  import { UpdateClassificationDto } from './classification.dto';
  import { CustomFieldInputDto } from './custom-field-metadata.dto';
  import { ProductInventoryDto } from './product-inventory.dto';
  import { DigitalProductDto } from './digital-product.dto';
  import { EventProductDto } from './event-product.dto';
  import { ServiceProductDto } from './service-product.dto';
  import { IsValidAdvancedInfo } from '../validators/product-advanced-info.validator';

  /**
   * DTO cho việc cập nhật sản phẩm trong module business
   */
  export class BusinessUpdateProductDto {
    @ApiProperty({
      description: 'Tên sản phẩm',
      example: 'Áo thun nam cao cấp',
      maxLength: 255,
      required: false,
    })
    @IsOptional()
    @IsString()
    @MaxLength(255)
    name?: string;

    @ApiProperty({
      description: 'Loại sản phẩm',
      enum: ProductTypeEnum,
      example: ProductTypeEnum.PHYSICAL,
      required: false,
    })
    @IsOptional()
    @IsEnum(ProductTypeEnum)
    productType?: ProductTypeEnum;

    @ApiProperty({
      description: 'Giá sản phẩm',
      oneOf: [
        { $ref: '#/components/schemas/HasPriceDto' },
        { $ref: '#/components/schemas/StringPriceDto' },
        { type: 'null' }
      ],
      example: {
        listPrice: 200000,
        salePrice: 150000,
        currency: 'VND'
      },
      required: false,
    })
    @IsOptional()
    price?: HasPriceDto | StringPriceDto | null;

    @ApiProperty({
      description: 'Loại giá',
      enum: PriceTypeEnum,
      example: PriceTypeEnum.HAS_PRICE,
      required: false,
    })
    @IsOptional()
    @IsEnum(PriceTypeEnum)
    typePrice?: PriceTypeEnum;

    @ApiProperty({
      description: 'Mô tả sản phẩm',
      example: 'Áo thun nam chất liệu cotton 100%',
      required: false,
    })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({
      description: 'Danh sách thao tác ảnh',
      type: 'array',
      items: {
        type: 'object',
        properties: {
          operation: { type: 'string', enum: ['ADD', 'DELETE'], example: 'ADD' },
          position: { type: 'number', example: 1, description: 'Vị trí ảnh cần xóa (cho DELETE)' },
          key: { type: 'string', example: 'uploads/user_products/2025/05/image.jpg', description: 'Khóa tệp (cho DELETE)' },
          mimeType: { type: 'string', example: 'image/png', description: 'Loại MIME (cho ADD)' }
        }
      },
      example: [
        {
          operation: 'ADD',
          mimeType: 'image/jpeg'
        },
        {
          operation: 'DELETE',
          position: 0
        }
      ],
      required: false,
    })
    @IsOptional()
    @IsArray()
    images?: Array<{
      operation: 'ADD' | 'DELETE';
      position?: number;
      key?: string;
      mimeType?: string;
    }>;

    @ApiProperty({
      description: 'Danh sách tag',
      type: [String],
      example: ['áo thun', 'nam', 'cotton', 'cao cấp'],
      required: false,
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    tags?: string[];

    @ApiProperty({
      description: 'Danh sách custom fields cho sản phẩm',
      type: [CustomFieldInputDto],
      required: false,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CustomFieldInputDto)
    customFields?: CustomFieldInputDto[];

    @ApiProperty({
      description: 'Cấu hình vận chuyển',
      type: BusinessShipmentConfigDto,
      required: false,
    })
    @IsOptional()
    @IsObject()
    @ValidateNested()
    @Type(() => BusinessShipmentConfigDto)
    shipmentConfig?: BusinessShipmentConfigDto;

    @ApiProperty({
      description: 'Danh sách phân loại sản phẩm',
      type: [UpdateClassificationDto],
      required: false,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => UpdateClassificationDto)
    classifications?: UpdateClassificationDto[];

    @ApiProperty({
      description: 'Thông tin tồn kho sản phẩm',
      type: ProductInventoryDto,
      required: false,
      example: {
        warehouseId: 1,
        availableQuantity: 100,
        sku: 'SKU-001',
        barcode: '1234567890123'
      }
    })
    @IsOptional()
    @IsObject()
    @ValidateNested()
    @Type(() => ProductInventoryDto)
    inventory?: ProductInventoryDto;

    @ApiProperty({
      description: 'Thông tin nâng cao cho sản phẩm (chỉ áp dụng cho DIGITAL, EVENT, SERVICE)',
      oneOf: [
        { $ref: '#/components/schemas/DigitalProductDto' },
        { $ref: '#/components/schemas/EventProductDto' },
        { $ref: '#/components/schemas/ServiceProductDto' }
      ],
      required: false,
    })
    @IsOptional()
    @IsObject()
    @ValidateNested()
    @IsValidAdvancedInfo()
    advancedInfo?: DigitalProductDto | EventProductDto | ServiceProductDto;
  }
