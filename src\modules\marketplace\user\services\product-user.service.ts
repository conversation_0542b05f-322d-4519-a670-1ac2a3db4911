import { Injectable, Logger } from '@nestjs/common';
import { ProductRepository } from '@modules/marketplace/repositories';
import { ProductHelper, ValidationHelper } from '@modules/marketplace/helpers';
import {CreateProductDto, DeleteMultipleProductsDto, PresignedUrlDto, PresignedUrlImageDto, QueryUserProductDto, UpdateProductDto, UpdateProductOption} from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { UserProductResponseDto } from '../dto/product-response.dto';
import { ProductDetailResponseDto, UserProductDetailResponseDto } from '@modules/marketplace/user/dto';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { ProductStatus } from '@modules/marketplace/enums';
import {FileSizeEnum, FileTypeEnum, ImageTypeEnum} from '@/shared/utils/file';
import {
  CategoryFolderEnum,
  generateS3Key,
} from '@/shared/utils/generators/s3-key-generator.util';
import { S3Service } from '@/shared/services/s3.service';
import { TimeIntervalEnum } from '@/shared/utils/time';
import { Product } from '@modules/marketplace/entities/product.entity';
import { Transactional } from 'typeorm-transactional';
import { CreateProductResponseDto, UpdateProductResponseDto } from '../dto';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { AgentRepository } from '@modules/agent/repositories';
import { UserDataFineTuneRepository, AdminDataFineTuneRepository } from '@modules/models/repositories';

@Injectable()
export class ProductUserService {
  private readonly logger = new Logger(ProductUserService.name);

  constructor(
    private readonly productRepository: ProductRepository,
    private readonly productHelper: ProductHelper,
    private readonly validationHelper: ValidationHelper,
    private readonly s3Service: S3Service,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly agentRepository: AgentRepository,
    private readonly userDataFineTuneRepository: UserDataFineTuneRepository,
    private readonly adminDataFineTuneRepository: AdminDataFineTuneRepository,
  ) {}

  /**
   * Lấy danh sách sản phẩm của người dùng hiện tại
   * @param userId ID của người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Danh sách sản phẩm phân trang
   */
  @Transactional()
  async getUserProducts(
    userId: number,
    queryDto: QueryUserProductDto,
  ): Promise<PaginatedResult<UserProductResponseDto>> {
    try {
      // Lấy danh sách sản phẩm từ repository
      const productsResult = await this.productRepository.findUserProducts(
        queryDto,
        userId,
      );

      // Lọc các sản phẩm đã bị xóa (nếu có) và chuyển đổi từ entity sang DTO
      const items = productsResult.items
        .filter(product => product.status !== ProductStatus.DELETED)
        .map(product => this.productHelper.mapToUserProductResponseDto(product));

      // Cập nhật meta để phản ánh số lượng sản phẩm sau khi lọc
      const filteredMeta = {
        ...productsResult.meta,
        itemCount: items.length
      };

      // Trả về kết quả phân trang
      return {
        items,
        meta: filteredMeta,
      };
    } catch (error) {
      this.logger.error(`Failed to get user products: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
        `Không thể lấy danh sách sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết sản phẩm của người dùng hiện tại theo ID
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm
   * @returns Thông tin chi tiết sản phẩm
   */
  @Transactional()
  async getUserProductById(
    userId: number,
    productId: number,
  ): Promise<UserProductDetailResponseDto> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về người dùng không
      const product = await this.productRepository.findByIdAndUserId(productId, userId);

      // Kiểm tra sản phẩm có tồn tại và không bị xóa
      this.validationHelper.validateProductNotDeleted(product);

      // Kiểm tra sản phẩm có thuộc về người dùng
      this.validationHelper.validateProductOwnership(product, userId);

      // Chuyển đổi từ entity sang DTO
      return this.productHelper.mapToUserProductDetailResponseDto(product);
    } catch (error) {
      this.logger.error(`Failed to get user product details: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
        `Không thể lấy thông tin chi tiết sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo presigned URL cho việc tải lên tài liệu sản phẩm
   * @param fileName Tên file
   * @param fileType Loại tài liệu
   * @returns URL ký trước để tải lên tài liệu
   */
  @Transactional()
  async createDocumentUploadUrl(
      fileName?: string,
      fileType: FileTypeEnum = FileTypeEnum.PDF,
  ): Promise<PresignedUrlDto> {
    try {
      // Tạo S3 key cho tài liệu sản phẩm
      const key = generateS3Key({
        baseFolder: 'marketplace',
        categoryFolder: CategoryFolderEnum.DOCUMENT,
        fileName: fileName || 'product-document',
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const url = await this.s3Service.createPresignedWithID(
          key,
          TimeIntervalEnum.THIRTY_MINUTES,
          fileType,
          FileSizeEnum.TEN_MB,
      );

      // Tính thời gian hết hạn (30 phút từ hiện tại)
      const expiresAt = Date.now() + TimeIntervalEnum.THIRTY_MINUTES * 1000;

      // Trả về object PresignedUrlDto
      return {
        url,
        key,
        expiresAt,
      };
    } catch (error) {
      this.logger.error(
          `Failed to create document upload URL: ${error.message}`,
          error.stack,
      );
      throw new AppException(
          MARKETPLACE_ERROR_CODES.PRESIGNED_URL_CREATION_FAILED,
          `Không thể tạo URL upload tài liệu: ${error.message}`,
      );
    }
  }

  /**
   * Tạo presigned URL cho việc tải lên hình ảnh sản phẩm
   * @param fileName Tên file
   * @param mediaType Loại media
   * @returns URL ký trước để tải lên hình ảnh
   */
  @Transactional()
  async createImageUploadUrl(
      fileName?: string,
      mediaType: ImageTypeEnum = ImageTypeEnum.PNG,
  ): Promise<PresignedUrlDto> {
    try {
      // Tạo S3 key cho hình ảnh sản phẩm
      const key = generateS3Key({
        baseFolder: 'marketplace',
        categoryFolder: CategoryFolderEnum.IMAGE,
        fileName: fileName || 'product-image',
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const url = await this.s3Service.createPresignedWithID(
          key,
          TimeIntervalEnum.FIFTEEN_MINUTES,
          mediaType,
          FileSizeEnum.FIVE_MB,
      );

      // Tính thời gian hết hạn (15 phút từ hiện tại)
      const expiresAt = Date.now() + TimeIntervalEnum.FIFTEEN_MINUTES * 1000;

      // Trả về object PresignedUrlDto
      return {
        url,
        key,
        expiresAt,
      };
    } catch (error) {
      this.logger.error(
          `Failed to create image upload URL: ${error.message}`,
          error.stack,
      );
      throw new AppException(
          MARKETPLACE_ERROR_CODES.PRESIGNED_URL_CREATION_FAILED,
          `Không thể tạo URL upload hình ảnh: ${error.message}`,
      );
    }
  }

  /**
   * Tạo sản phẩm mới với trạng thái mặc định là DRAFT
   * @param userId ID của người dùng hiện tại
   * @param createProductDto Dữ liệu tạo sản phẩm
   * @returns Sản phẩm đã tạo cùng với các URL để upload tài liệu
   */
  @Transactional()
  async createProduct(userId: number, createProductDto: CreateProductDto): Promise<CreateProductResponseDto> {
    try {
      const now = Date.now();

      // Kiểm tra trạng thái APPROVED của tài nguyên gốc trước khi tạo sản phẩm
      await this.validationHelper.validateSourceResourceStatus(
        createProductDto.sourceId || null,
        createProductDto.category,
        {
          knowledgeFileRepository: this.knowledgeFileRepository,
          agentRepository: this.agentRepository,
          userDataFineTuneRepository: this.userDataFineTuneRepository,
          adminDataFineTuneRepository: this.adminDataFineTuneRepository,
        }
      );

      // Tạo sản phẩm mới
      const product = new Product();
      product.name = createProductDto.name;
      product.description = createProductDto.description;
      product.listedPrice = createProductDto.listedPrice;
      product.discountedPrice = createProductDto.discountedPrice;
      product.category = createProductDto.category;
      product.sourceId = createProductDto.sourceId || null;
      product.status = createProductDto.status || ProductStatus.DRAFT;
      product.userId = userId;
      product.createdAt = now;
      product.updatedAt = now;
      product.images = []; // Khởi tạo mảng rỗng, sẽ được cập nhật sau khi upload

      // Tạo các URL upload cho hình ảnh và lưu keys vào database
      const imagesUploadUrls: Array<{url: string; key: string; index: number}> = [];
      const imageEntries: Array<{key: string; position: number}> = [];

      if (createProductDto.imagesMediaTypes && createProductDto.imagesMediaTypes.length > 0) {
        for (let i = 0; i < createProductDto.imagesMediaTypes.length; i++) {
          try {
            const mediaType = createProductDto.imagesMediaTypes[i] as ImageTypeEnum;
            const imageUploadUrl = await this.createImageUploadUrl(
              `product-image-${i}-${now}`,
              mediaType
            );

            // Lưu key và position vào mảng để cập nhật database
            imageEntries.push({
              key: imageUploadUrl.key,
              position: i
            });

            imagesUploadUrls.push({
              url: imageUploadUrl.url,
              key: imageUploadUrl.key,
              index: i
            });
          } catch (error) {
            this.logger.error(`Failed to create image upload URL at index ${i}: ${error.message}`, error.stack);
          }
        }
      }

      // Cập nhật mảng images
      product.images = imageEntries;

      // Tạo URL upload cho hướng dẫn sử dụng
      let userManualUploadUrl: PresignedUrlDto | undefined = undefined;
      if (createProductDto.userManualMediaType) {
        try {
          const fileType = createProductDto.userManualMediaType as FileTypeEnum;
          userManualUploadUrl = await this.createDocumentUploadUrl(
            `product-manual-${now}`,
            fileType
          );

          // Lưu key vào database
          product.userManual = userManualUploadUrl.key;
        } catch (error) {
          this.logger.error(`Failed to create user manual upload URL: ${error.message}`, error.stack);
        }
      }

      // Tạo URL upload cho thông tin chi tiết
      let detailUploadUrl: PresignedUrlDto | undefined = undefined;
      if (createProductDto.detailMediaType) {
        try {
          const fileType = createProductDto.detailMediaType as FileTypeEnum;
          detailUploadUrl = await this.createDocumentUploadUrl(
            `product-detail-${now}`,
            fileType
          );

          // Lưu key vào database
          product.detail = detailUploadUrl.key;
        } catch (error) {
          this.logger.error(`Failed to create detail upload URL: ${error.message}`, error.stack);
        }
      }

      // Lưu sản phẩm vào database chỉ một lần duy nhất
      const savedProduct = await this.productRepository.save(product);

      // Lấy thông tin người bán
      const sellerInfo = this.productHelper.extractSellerInfo(savedProduct);

      // Tạo response
      return {
        id: savedProduct.id.toString(),
        name: savedProduct.name,
        description: savedProduct.description,
        listedPrice: savedProduct.listedPrice.toString(),
        discountedPrice: savedProduct.discountedPrice.toString(),
        category: savedProduct.category ? savedProduct.category.toString() : '',
        sourceId: savedProduct.sourceId || '',
        createdAt: savedProduct.createdAt.toString(),
        updatedAt: savedProduct.updatedAt.toString(),
        seller: {
          name: sellerInfo.name,
          avatar: sellerInfo.avatar || '',
          type: sellerInfo.type
        },
        status: savedProduct.status,
        uploadUrls: {
          productId: savedProduct.id.toString(),
          imagesUploadUrls,
          ...(userManualUploadUrl ? { userManualUploadUrl } : {}),
          ...(detailUploadUrl ? { detailUploadUrl } : {})
        }
      };
    } catch (error) {
      this.logger.error(`Failed to create product: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Không thể tạo sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Gửi duyệt sản phẩm (chuyển trạng thái từ DRAFT sang PENDING)
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm
   * @returns Sản phẩm đã được cập nhật
   */
  @Transactional()
  async submitProductForApproval(userId: number, productId: number): Promise<UserProductDetailResponseDto> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về người dùng không
      const product = await this.productRepository.findByIdAndUserId(productId, userId);

      // Nếu không tìm thấy sản phẩm
      if (!product) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại'
        );
      }

      // Kiểm tra sản phẩm có tồn tại và không bị xóa
      this.validationHelper.validateProductNotDeleted(product);

      // Kiểm tra sản phẩm có thuộc về người dùng
      this.validationHelper.validateProductOwnership(product, userId);

      // Kiểm tra sản phẩm có ở trạng thái DRAFT không
      this.validationHelper.validateProductIsDraft(product);

      // Kiểm tra sản phẩm có đủ thông tin để gửi duyệt không
      this.validationHelper.validateProductForSubmission(product);

      // Cập nhật trạng thái sản phẩm
      product.status = ProductStatus.PENDING;
      product.updatedAt = Date.now();

      // Log ID sản phẩm trước khi cập nhật
      this.logger.log(`Đang gửi duyệt sản phẩm với ID: ${product.id}`);

      // Sử dụng updateProduct thay vì save để đảm bảo cập nhật đúng sản phẩm
      // Chỉ giữ lại các trường thuộc về entity Product
      const updateData = {
        id: product.id,
        name: product.name,
        description: product.description,
        listedPrice: product.listedPrice,
        discountedPrice: product.discountedPrice,
        images: product.images,
        userManual: product.userManual,
        detail: product.detail,
        status: product.status,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        employeeId: product.employeeId,
        userId: product.userId,
        sourceId: product.sourceId,
        category: product.category
      };
      const updatedProduct = await this.productRepository.updateProduct(product.id, updateData);

      this.logger.log(`Sản phẩm sau khi gửi duyệt có ID: ${updatedProduct.id}`);

      // Cập nhật trạng thái is_for_sale = true cho tài nguyên gốc
      if (updatedProduct.sourceId && updatedProduct.category) {
        this.logger.log(`Cập nhật trạng thái is_for_sale = true cho sourceId: ${updatedProduct.sourceId}, category: ${updatedProduct.category}`);
        const updateResult = await this.productRepository.updateIsForSale(updatedProduct.sourceId, true, updatedProduct.category);
        if (!updateResult) {
          this.logger.warn(`Không thể cập nhật trạng thái is_for_sale cho sourceId: ${updatedProduct.sourceId}`);
        }
      } else {
        this.logger.warn(`Không thể cập nhật trạng thái is_for_sale: sourceId=${updatedProduct.sourceId}, category=${updatedProduct.category}`);
      }

      // Chuyển đổi từ entity sang DTO
      return this.productHelper.mapToUserProductDetailResponseDto(updatedProduct);
    } catch (error) {
      this.logger.error(`Failed to submit product for approval: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED,
        `Không thể gửi sản phẩm để duyệt: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều sản phẩm (chuyển trạng thái sang DELETED)
   * @param userId ID của người dùng hiện tại
   * @param deleteMultipleProductsDto DTO chứa danh sách ID sản phẩm cần xóa
   * @returns Danh sách ID sản phẩm đã xóa thành công và danh sách ID sản phẩm thất bại
   */
  @Transactional()
  async deleteMultipleProducts(
    userId: number,
    deleteMultipleProductsDto: DeleteMultipleProductsDto,
  ): Promise<{
    successIds: number[];
    failedIds: { id: number; reason: string }[];
  }> {
    const { productIds } = deleteMultipleProductsDto;
    const successIds: number[] = [];
    const failedIds: { id: number; reason: string }[] = [];

    this.logger.log(
      `User ${userId} đang xóa ${productIds.length} sản phẩm: ${productIds.join(', ')}`,
    );

    // Xử lý từng sản phẩm một cách tuần tự để đảm bảo validation đúng
    for (const productId of productIds) {
      try {
        // Kiểm tra sản phẩm có tồn tại và thuộc về người dùng không
        const product = await this.productRepository.findByIdAndUserId(productId, userId);

        // Nếu không tìm thấy sản phẩm
        if (!product) {
          failedIds.push({
            id: productId,
            reason: 'Sản phẩm không tồn tại',
          });
          continue;
        }

        // Kiểm tra sản phẩm có tồn tại và không bị xóa
        try {
          this.validationHelper.validateProductNotDeleted(product);
        } catch (error) {
          failedIds.push({
            id: productId,
            reason: error instanceof AppException ? error.message : 'Sản phẩm đã bị xóa',
          });
          continue;
        }

        // Kiểm tra sản phẩm có thuộc về người dùng
        try {
          this.validationHelper.validateProductOwnership(product, userId);
        } catch (error) {
          failedIds.push({
            id: productId,
            reason: error instanceof AppException ? error.message : 'Không có quyền xóa sản phẩm này',
          });
          continue;
        }

        // Chỉ cho phép xóa sản phẩm ở trạng thái DRAFT
        if (product.status !== ProductStatus.DRAFT) {
          failedIds.push({
            id: productId,
            reason: `Chỉ có thể xóa sản phẩm ở trạng thái DRAFT. Sản phẩm hiện tại đang ở trạng thái ${product.status}.`,
          });
          continue;
        }

        // Cập nhật trạng thái sản phẩm
        product.status = ProductStatus.DELETED;
        product.updatedAt = Date.now();

        // Sử dụng updateProduct thay vì save để đảm bảo cập nhật đúng sản phẩm
        const updateData = {
          id: product.id,
          name: product.name,
          description: product.description,
          listedPrice: product.listedPrice,
          discountedPrice: product.discountedPrice,
          images: product.images,
          userManual: product.userManual,
          detail: product.detail,
          status: product.status,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
          employeeId: product.employeeId,
          userId: product.userId,
          sourceId: product.sourceId,
          category: product.category
        };
        await this.productRepository.updateProduct(product.id, updateData);

        successIds.push(productId);
        this.logger.log(`Sản phẩm với ID: ${product.id} đã được đánh dấu xóa`);
      } catch (error) {
        this.logger.error(
          `Error deleting product ${productId}: ${error.message}`,
          error.stack,
        );
        failedIds.push({
          id: productId,
          reason: error instanceof AppException ? error.message : `Lỗi khi xóa sản phẩm: ${error.message}`,
        });
      }
    }

    this.logger.log(
      `Kết quả xóa sản phẩm: ${successIds.length} thành công, ${failedIds.length} thất bại`,
    );

    return { successIds, failedIds };
  }

  /**
   * Lấy chi tiết sản phẩm
   * @param productId ID của sản phẩm
   * @param currentUserId ID của người dùng hiện tại
   * @returns Thông tin chi tiết sản phẩm
   */
  @Transactional()
  async getProductDetail(productId: number, currentUserId: number): Promise<ProductDetailResponseDto> {
    try {
      // Kiểm tra sản phẩm có tồn tại không - truyền currentUserId để tính isPurchased
      const product = await this.productRepository.findProductDetailById(productId, currentUserId);

      // Kiểm tra sản phẩm có tồn tại
      if (!product) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại hoặc đã bị xóa'
        );
      }

      // Kiểm tra sản phẩm không bị xóa
      this.validationHelper.validateProductNotDeleted(product);

      // Kiểm tra sản phẩm không thuộc về người dùng hiện tại
      if (product.userId === currentUserId) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.CANNOT_BUY_OWN_PRODUCT,
          'Không thể xem chi tiết sản phẩm của chính mình, vui lòng sử dụng API quản lý sản phẩm của bạn'
        );
      }

      // Chuyển đổi từ entity sang DTO
      return this.productHelper.mapToProductDetailResponseDto(product);
    } catch (error) {
      this.logger.error(`Failed to get product detail: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
        `Không thể lấy thông tin chi tiết sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách sản phẩm được phê duyệt, không thuộc về người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @param currentUserId ID của người dùng hiện tại
   * @returns Danh sách sản phẩm được phê duyệt
   */
  @Transactional()
  async getApprovedProducts(queryDto: QueryUserProductDto, currentUserId: number): Promise<PaginatedResult<UserProductResponseDto>> {
    try {
      // Lấy danh sách sản phẩm từ repository
      const productsResult = await this.productRepository.findApprovedProducts(
        queryDto,
        currentUserId,
      );

      // Chuyển đổi từ entity sang DTO
      const items = productsResult.items.map(product =>
        this.productHelper.mapToUserProductResponseDto(product),
      );

      return {
        items,
        meta: productsResult.meta,
      };
    } catch (error) {
      this.logger.error(`Failed to get approved products: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
        `Không thể lấy danh sách sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin sản phẩm với hai tùy chọn: lưu nháp hoặc gửi duyệt
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm cần cập nhật
   * @param updateProductDto Dữ liệu cập nhật sản phẩm, bao gồm tùy chọn lưu nháp hoặc gửi duyệt
   * @returns Sản phẩm đã cập nhật và các URL để upload tài liệu mới
   */
  @Transactional()
  async updateProduct(userId: number, productId: number, updateProductDto: UpdateProductDto): Promise<UpdateProductResponseDto> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về người dùng không
      const product = await this.productRepository.findByIdAndUserId(productId, userId);

      // Nếu không tìm thấy sản phẩm
      if (!product) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại'
        );
      }

      // Kiểm tra sản phẩm có tồn tại và không bị xóa
      this.validationHelper.validateProductNotDeleted(product);

      // Kiểm tra sản phẩm có thuộc về người dùng
      this.validationHelper.validateProductOwnership(product, userId);

      // Kiểm tra sản phẩm có ở trạng thái DRAFT không
      this.validationHelper.validateProductIsDraft(product);

      // Cập nhật thông tin cơ bản của sản phẩm
      if (updateProductDto.productInfo) {
        product.name = updateProductDto.productInfo.name;
        product.listedPrice = updateProductDto.productInfo.listedPrice;
        product.discountedPrice = updateProductDto.productInfo.discountedPrice;
        if (updateProductDto.productInfo.description) {
          product.description = updateProductDto.productInfo.description;
        }
      }

      // Xử lý thao tác với ảnh
      const imagesUploadUrls: Array<PresignedUrlImageDto> = [];
      if (updateProductDto.images && updateProductDto.images.length > 0) {
        // Xử lý các thao tác DELETE trước
        const deleteOperations = updateProductDto.images.filter(img => img.operation === 'DELETE');
        for (const deleteOp of deleteOperations) {
          // Xóa theo key nếu có
          if (deleteOp.key) {
            // Lọc ra các ảnh không bị xóa theo key
            product.images = product.images.filter(img => img.key !== deleteOp.key);
          }
          // Xóa theo position nếu có
          else if (deleteOp.position !== undefined) {
            // Lọc ra các ảnh không bị xóa theo position
            product.images = product.images.filter(img => img.position !== deleteOp.position);
          }
        }

        // Xử lý các thao tác ADD
        const addOperations = updateProductDto.images.filter(img => img.operation === 'ADD');

        for (const addOp of addOperations) {
          if (addOp.mimeType) {
            try {
              // Tìm vị trí lớn nhất hiện tại và tăng lên 1 để có vị trí mới
              const maxPosition = product.images.length > 0
                ? Math.max(...product.images.map(img => img.position))
                : -1;
              const newPosition = maxPosition + 1;

              const mediaType = addOp.mimeType as ImageTypeEnum;

              // Sử dụng phương thức createImageUploadUrl giống như trong createProduct
              const imageUploadUrl = await this.createImageUploadUrl(
                `product-image-${newPosition}-${Date.now()}`,
                mediaType
              );

              const key = imageUploadUrl.key;
              const url = imageUploadUrl.url;

              // Thêm vào danh sách ảnh của sản phẩm với vị trí mới
              product.images.push({
                key,
                position: newPosition
              });

              // Thêm vào danh sách URL upload
              imagesUploadUrls.push({
                index: newPosition,
                uploadUrl: url
              });
            } catch (error) {
              this.logger.error(`Failed to create image upload URL: ${error.message}`, error.stack);
            }
          }
        }

        // Sắp xếp lại mảng images theo position
        product.images.sort((a, b) => a.position - b.position);
      }

      // Xử lý cập nhật chi tiết sản phẩm
      let presignedUrlDetail: string | null = null;
      if (updateProductDto.detailEdited) {
        try {
          const detailUploadUrl = await this.createDocumentUploadUrl(
            `product-detail-${Date.now()}`,
            FileTypeEnum.HTML
          );

          // Cập nhật key trong database
          product.detail = detailUploadUrl.key;
          presignedUrlDetail = detailUploadUrl.url;
        } catch (error) {
          this.logger.error(`Failed to create detail upload URL: ${error.message}`, error.stack);
        }
      }

      // Xử lý cập nhật hướng dẫn sử dụng
      let presignedUrlUserManual: string | null = null;
      if (updateProductDto.userManual) {
        try {
          const userManualUploadUrl = await this.createDocumentUploadUrl(
            `product-manual-${Date.now()}`,
            FileTypeEnum.PDF
          );

          // Cập nhật key trong database
          product.userManual = userManualUploadUrl.key;
          presignedUrlUserManual = userManualUploadUrl.url;
        } catch (error) {
          this.logger.error(`Failed to create user manual upload URL: ${error.message}`, error.stack);
        }
      }

      // Cập nhật thời gian
      product.updatedAt = Date.now();

      // Xử lý tùy chọn cập nhật (lưu nháp hoặc gửi duyệt)
      if (updateProductDto.updateOption === UpdateProductOption.SUBMIT_FOR_APPROVAL) {
        // Kiểm tra sản phẩm có đủ thông tin để gửi duyệt không
        this.validationHelper.validateProductForSubmission(product);

        // Cập nhật trạng thái sản phẩm thành PENDING (chờ duyệt)
        product.status = ProductStatus.PENDING;
        this.logger.log(`Sản phẩm ${productId} đã được gửi duyệt bởi người dùng ${userId}`);
      } else {
        // Mặc định là lưu nháp, giữ nguyên trạng thái DRAFT
        product.status = ProductStatus.DRAFT;
        this.logger.log(`Sản phẩm ${productId} đã được lưu nháp bởi người dùng ${userId}`);
      }

      // Log ID sản phẩm trước khi cập nhật
      this.logger.log(`Đang cập nhật sản phẩm với ID: ${product.id}`);

      // Sử dụng updateProduct thay vì save để đảm bảo cập nhật đúng sản phẩm
      // Chỉ giữ lại các trường thuộc về entity Product
      const updateData = {
        id: product.id,
        name: product.name,
        description: product.description,
        listedPrice: product.listedPrice,
        discountedPrice: product.discountedPrice,
        images: product.images,
        userManual: product.userManual,
        detail: product.detail,
        status: product.status,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        employeeId: product.employeeId,
        userId: product.userId,
        sourceId: product.sourceId,
        category: product.category
      };
      const updatedProduct = await this.productRepository.updateProduct(product.id, updateData);

      this.logger.log(`Sản phẩm sau khi cập nhật có ID: ${updatedProduct.id}`);

      // Cập nhật trạng thái is_for_sale cho tài nguyên gốc nếu chuyển sang PENDING
      if (updatedProduct.sourceId && updatedProduct.category && updatedProduct.status === ProductStatus.PENDING) {
        this.logger.log(`Cập nhật trạng thái is_for_sale = true cho sourceId: ${updatedProduct.sourceId}, category: ${updatedProduct.category}`);
        const updateResult = await this.productRepository.updateIsForSale(updatedProduct.sourceId, true, updatedProduct.category);
        if (!updateResult) {
          this.logger.warn(`Không thể cập nhật trạng thái is_for_sale cho sourceId: ${updatedProduct.sourceId}`);
        }
      } else if (updatedProduct.status === ProductStatus.PENDING) {
        this.logger.warn(`Không thể cập nhật trạng thái is_for_sale: sourceId=${updatedProduct.sourceId}, category=${updatedProduct.category}`);
      }

      // Chuyển đổi từ entity sang DTO
      const productDto = this.productHelper.mapToUserProductDetailResponseDto(updatedProduct);

      // Tạo response
      return {
        product: productDto,
        presignedUrlImage: imagesUploadUrls,
        presignedUrlDetail,
        presignedUrlUserManual
      };
    } catch (error) {
      this.logger.error(`Failed to update product: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Không thể cập nhật sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Hủy gửi duyệt sản phẩm (chuyển trạng thái từ PENDING về DRAFT)
   * @param userId ID của người dùng hiện tại
   * @param productId ID của sản phẩm
   * @returns Sản phẩm đã được cập nhật
   */
  @Transactional()
  async cancelSubmission(userId: number, productId: number): Promise<UserProductDetailResponseDto> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về người dùng không
      const product = await this.productRepository.findByIdAndUserId(productId, userId);

      // Nếu không tìm thấy sản phẩm
      if (!product) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại'
        );
      }

      // Kiểm tra sản phẩm có tồn tại và không bị xóa
      this.validationHelper.validateProductNotDeleted(product);

      // Kiểm tra sản phẩm có thuộc về người dùng
      this.validationHelper.validateProductOwnership(product, userId);

      // Kiểm tra sản phẩm có ở trạng thái PENDING không
      this.validationHelper.validateProductIsPending(product);

      // Cập nhật trạng thái sản phẩm
      product.status = ProductStatus.DRAFT;
      product.updatedAt = Date.now();

      // Log ID sản phẩm trước khi cập nhật
      this.logger.log(`Đang hủy gửi duyệt sản phẩm với ID: ${product.id}`);

      // Sử dụng updateProduct thay vì save để đảm bảo cập nhật đúng sản phẩm
      // Chỉ giữ lại các trường thuộc về entity Product
      const updateData = {
        id: product.id,
        name: product.name,
        description: product.description,
        listedPrice: product.listedPrice,
        discountedPrice: product.discountedPrice,
        images: product.images,
        userManual: product.userManual,
        detail: product.detail,
        status: product.status,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        employeeId: product.employeeId,
        userId: product.userId,
        sourceId: product.sourceId,
        category: product.category
      };
      const updatedProduct = await this.productRepository.updateProduct(product.id, updateData);

      this.logger.log(`Sản phẩm sau khi hủy gửi duyệt có ID: ${updatedProduct.id}`);

      // Cập nhật trạng thái is_for_sale = false cho tài nguyên gốc
      if (updatedProduct.sourceId && updatedProduct.category) {
        this.logger.log(`Cập nhật trạng thái is_for_sale = false cho sourceId: ${updatedProduct.sourceId}, category: ${updatedProduct.category}`);
        const updateResult = await this.productRepository.updateIsForSale(updatedProduct.sourceId, false, updatedProduct.category);
        if (!updateResult) {
          this.logger.warn(`Không thể cập nhật trạng thái is_for_sale cho sourceId: ${updatedProduct.sourceId}`);
        }
      } else {
        this.logger.warn(`Không thể cập nhật trạng thái is_for_sale: sourceId=${updatedProduct.sourceId}, category=${updatedProduct.category}`);
      }

      // Chuyển đổi từ entity sang DTO
      return this.productHelper.mapToUserProductDetailResponseDto(updatedProduct);
    } catch (error) {
      this.logger.error(`Failed to cancel product submission: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED,
        `Không thể hủy gửi duyệt sản phẩm: ${error.message}`,
      );
    }
  }
}
