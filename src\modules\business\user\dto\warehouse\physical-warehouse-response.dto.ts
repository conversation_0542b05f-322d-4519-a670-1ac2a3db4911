import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { WarehouseCustomFieldResponseDto } from './warehouse-custom-field-response.dto';

/**
 * DTO cho phản hồi thông tin kho vật lý
 */
export class PhysicalWarehouseResponseDto {
  /**
   * ID của kho (primary key)
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của kho (primary key)',
    example: 1,
  })
  id: number;

  /**
   * ID của kho (warehouse ID)
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của kho (warehouse ID)',
    example: 1,
  })
  warehouseId: number;

  /**
   * Tên kho
   * @example "Kho hàng chính"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên kho',
    example: '<PERSON>ho hàng chính',
  })
  name: string;

  /**
   * <PERSON><PERSON> tả kho
   * @example "Kho chứa các sản phẩm chính của công ty"
   */
  @Expose()
  @ApiProperty({
    description: 'Mô tả kho',
    example: 'Kho chứa các sản phẩm chính của công ty',
  })
  description: string;

  /**
   * Loại kho
   * @example "PHYSICAL"
   */
  @Expose()
  @ApiProperty({
    description: 'Loại kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.PHYSICAL,
  })
  type: WarehouseTypeEnum;

  /**
   * Địa chỉ kho
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @Expose()
  @ApiProperty({
    description: 'Địa chỉ kho',
    example: '123 Đường ABC, Quận 1, TP.HCM',
  })
  address: string;

  /**
   * Sức chứa kho
   * @example 1000
   */
  @Expose()
  @ApiProperty({
    description: 'Sức chứa kho',
    example: 1000,
    required: false,
  })
  capacity: number | null;

  /**
   * Danh sách trường tùy chỉnh của kho
   */
  @Expose()
  @Type(() => WarehouseCustomFieldResponseDto)
  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh của kho',
    type: [WarehouseCustomFieldResponseDto],
    required: false,
  })
  customFields?: WarehouseCustomFieldResponseDto[];
}
