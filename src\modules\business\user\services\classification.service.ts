import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserClassificationRepository,

  CustomFieldRepository,
  UserProductRepository,
} from '@modules/business/repositories';
import {
  CreateClassificationDto,
  UpdateClassificationDto,
  ClassificationResponseDto,
  CustomFieldInputDto,
} from '../dto';
import { UserClassification } from '@modules/business/entities';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PriceTypeEnum } from '@modules/business/enums';
import { MetadataHelper } from '../helpers/metadata.helper';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@shared/utils/generators/s3-key-generator.util';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';

/**
 * Service xử lý logic nghiệp vụ cho phân loại sản phẩm
 */
@Injectable()
export class ClassificationService {
  private readonly logger = new Logger(ClassificationService.name);

  constructor(
    private readonly userClassificationRepository: UserClassificationRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly metadataHelper: MetadataHelper,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Tạo phân loại mới cho sản phẩm
   * @param productId ID của sản phẩm
   * @param createDto DTO chứa thông tin phân loại
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã tạo
   */
  @Transactional()
  async create(
    productId: number,
    createDto: CreateClassificationDto,
    userId: number,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Tạo phân loại mới cho sản phẩm ${productId}, userId=${userId}`);

      // Xử lý custom fields nếu có (classification custom fields không cần validate với database)
      if (createDto.customFields && createDto.customFields.length > 0) {
        this.logger.log(`Xử lý ${createDto.customFields.length} custom fields cho classification`);
      }

      // Lấy thông tin sản phẩm để kiểm tra loại giá và quyền sở hữu
      const product = await this.userProductRepository.findById(productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền tạo phân loại cho sản phẩm này`,
        );
      }

      // Kiểm tra giá sản phẩm theo loại giá
      this.validateClassificationPrice(createDto.price, product.typePrice, createDto.type);

      // Tạo metadata cho classification (không cần validate với database vì là custom fields tự tạo)
      const metadata = this.metadataHelper.buildClassificationMetadata(
        createDto.customFields || [],
        [], // Không lưu imagesMediaTypes vào metadata
        [] // Chưa có ảnh thực tế, sẽ được cập nhật sau khi có classification ID
      );

      // Tạo phân loại mới
      const classification = new UserClassification();
      classification.type = createDto.type;
      classification.price = createDto.price;
      classification.productId = productId;
      classification.metadata = metadata;

      // Lưu phân loại vào database
      const savedClassification = await this.userClassificationRepository.save(classification);

      // Cập nhật metadata với key ảnh nếu có imagesMediaTypes
      if (createDto.imagesMediaTypes && createDto.imagesMediaTypes.length > 0) {
        // Tạo key ảnh với classification ID thực tế
        const now = Date.now();
        const imageKeys = createDto.imagesMediaTypes.map((_, index) => {
          const fileName = `classification-${savedClassification.id}-image-${index}-${now}`;
          return generateS3Key({
            baseFolder: 'business',
            categoryFolder: CategoryFolderEnum.IMAGE,
            fileName: fileName,
            useTimeFolder: true,
          });
        });

        // Tạo thông tin ảnh với key
        const images = imageKeys.map((key, index) => ({
          key: key,
          position: index
        }));

        // Cập nhật metadata với thông tin ảnh (không lưu imagesMediaTypes)
        const updatedMetadata = this.metadataHelper.buildClassificationMetadata(
          createDto.customFields || [],
          [], // Không lưu imagesMediaTypes vào metadata
          images
        );

        savedClassification.metadata = updatedMetadata;
        await this.userClassificationRepository.save(savedClassification);
      }

      // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
      const customFieldsFromMetadata = savedClassification.metadata?.customFields || [];
      const customFieldsResponse = customFieldsFromMetadata.map(cf => ({
        customFieldId: cf.customFieldId,
        value: cf.value,
      }));

      // Đảm bảo price có đầy đủ thông tin
      let price = savedClassification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Tạo upload URLs cho ảnh classification nếu có (sử dụng key từ metadata)
      let classificationUploadUrls: any[] = [];
      if (createDto.imagesMediaTypes && createDto.imagesMediaTypes.length > 0) {
        const imagesFromMetadata = savedClassification.metadata?.images || [];

        for (let i = 0; i < imagesFromMetadata.length; i++) {
          try {
            const mediaType = createDto.imagesMediaTypes[i] as ImageTypeEnum;
            const imageKey = imagesFromMetadata[i].key;

            // Tạo presigned URL với key từ metadata
            const url = await this.s3Service.createPresignedWithID(
              imageKey,
              TimeIntervalEnum.FIFTEEN_MINUTES,
              mediaType,
              FileSizeEnum.FIVE_MB,
            );

            this.logger.debug(`Created presigned URL for classification image upload: ${imageKey} with position ${i}`);

            classificationUploadUrls.push({
              url: url,
              key: imageKey,
              index: i
            });
          } catch (error) {
            this.logger.error(`Lỗi khi tạo presigned URL cho ảnh classification: ${error.message}`, error.stack);
          }
        }
      }

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata = savedClassification.metadata?.images || [];
      const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: savedClassification.id,
        type: savedClassification.type,
        price,
        customFields: customFieldsResponse,
        imagesMediaTypes: createDto.imagesMediaTypes || [], // Từ input, không từ metadata
        images: imagesWithUrls, // Ảnh thực tế với URL CDN (lúc tạo sẽ rỗng)
        uploadUrls: classificationUploadUrls.length > 0 ? {
          classificationId: savedClassification.id,
          imagesUploadUrls: classificationUploadUrls
        } : undefined,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_CREATION_FAILED,
        `Lỗi khi tạo phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật phân loại
   * @param id ID của phân loại
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateClassificationDto,
    userId: number,
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Cập nhật phân loại với ID ${id}, userId=${userId}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra quyền sở hữu
      const product = await this.userProductRepository.findById(classification.productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${classification.productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền cập nhật phân loại này`,
        );
      }

      // Cập nhật các trường được cung cấp
      if (updateDto.type !== undefined) {
        classification.type = updateDto.type;
      }
      if (updateDto.price !== undefined) {
        // Kiểm tra giá sản phẩm theo loại giá
        this.validateClassificationPrice(updateDto.price, product.typePrice, classification.type);
        classification.price = updateDto.price;
      }

      // Xử lý các trường tùy chỉnh và imagesMediaTypes nếu có
      if (updateDto.customFields !== undefined || updateDto.imagesMediaTypes !== undefined) {
        // Lấy metadata hiện tại
        const currentMetadata = classification.metadata || { customFields: [] };

        // Sử dụng customFields từ updateDto nếu có, nếu không giữ nguyên
        const customFields = updateDto.customFields !== undefined
          ? updateDto.customFields
          : currentMetadata.customFields || [];

        // Không cần lấy imagesMediaTypes từ metadata nữa vì không lưu vào đó

        // Xử lý ảnh: nếu có imagesMediaTypes mới thì tạo key mới, nếu không thì giữ nguyên
        let images = currentMetadata.images || [];
        if (updateDto.imagesMediaTypes !== undefined) {
          // Tạo key ảnh mới với classification ID
          const now = Date.now();
          const imageKeys = updateDto.imagesMediaTypes.map((_, index) => {
            const fileName = `classification-${classification.id}-image-${index}-${now}`;
            return generateS3Key({
              baseFolder: 'business',
              categoryFolder: CategoryFolderEnum.IMAGE,
              fileName: fileName,
              useTimeFolder: true,
            });
          });

          // Tạo thông tin ảnh mới với key
          images = imageKeys.map((key, index) => ({
            key: key,
            position: index
          }));
        }

        // Cập nhật metadata với classification custom fields và ảnh (không lưu imagesMediaTypes)
        const newMetadata = this.metadataHelper.buildClassificationMetadata(
          customFields,
          [], // Không lưu imagesMediaTypes vào metadata
          images // Ảnh mới hoặc ảnh cũ
        );
        classification.metadata = newMetadata;
      }

      // Lưu phân loại vào database
      const updatedClassification = await this.userClassificationRepository.save(classification);

      // Lấy custom fields từ metadata (classification custom fields không có ID)
      const customFieldsFromMetadata = updatedClassification.metadata?.customFields || [];
      const customFieldsResponse = customFieldsFromMetadata.map(cf => ({
        label: cf.label,
        type: cf.type,
        required: cf.required || false,
        value: cf.value,
      }));

      // Đảm bảo price có đầy đủ thông tin
      let price = updatedClassification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Không lấy imagesMediaTypes từ metadata nữa vì không lưu vào đó

      // Tạo upload URLs cho ảnh classification nếu có imagesMediaTypes mới (sử dụng key từ metadata)
      let classificationUploadUrls: any[] = [];
      if (updateDto.imagesMediaTypes && updateDto.imagesMediaTypes.length > 0) {
        const imagesFromMetadata = updatedClassification.metadata?.images || [];

        for (let i = 0; i < imagesFromMetadata.length; i++) {
          try {
            const mediaType = updateDto.imagesMediaTypes[i] as ImageTypeEnum;
            const imageKey = imagesFromMetadata[i].key;

            // Tạo presigned URL với key từ metadata
            const url = await this.s3Service.createPresignedWithID(
              imageKey,
              TimeIntervalEnum.FIFTEEN_MINUTES,
              mediaType,
              FileSizeEnum.FIVE_MB,
            );

            this.logger.debug(`Created presigned URL for classification image upload: ${imageKey} with position ${i}`);

            classificationUploadUrls.push({
              url: url,
              key: imageKey,
              index: i
            });
          } catch (error) {
            this.logger.error(`Lỗi khi tạo presigned URL cho ảnh classification: ${error.message}`, error.stack);
          }
        }
      }

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata = updatedClassification.metadata?.images || [];
      const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: updatedClassification.id,
        type: updatedClassification.type,
        price,
        customFields: customFieldsResponse,
        imagesMediaTypes: updateDto.imagesMediaTypes || [], // Từ input, không từ metadata
        images: imagesWithUrls, // Ảnh thực tế với URL CDN
        uploadUrls: classificationUploadUrls.length > 0 ? {
          classificationId: updatedClassification.id,
          imagesUploadUrls: classificationUploadUrls
        } : undefined,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_UPDATE_FAILED,
        `Lỗi khi cập nhật phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa phân loại
   * @param id ID của phân loại
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa phân loại với ID ${id}, userId=${userId}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy thông tin sản phẩm để kiểm tra quyền sở hữu
      const product = await this.userProductRepository.findById(classification.productId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${classification.productId}`,
        );
      }

      // Kiểm tra quyền sở hữu
      if (product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
          `Bạn không có quyền xóa phân loại này`,
        );
      }

      // Metadata sẽ được xóa cùng với classification, không cần xử lý riêng

      // Xóa phân loại
      await this.userClassificationRepository.delete(id);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_DELETION_FAILED,
        `Lỗi khi xóa phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách phân loại theo ID sản phẩm
   * @param productId ID của sản phẩm
   * @returns Danh sách phân loại
   */
  async getByProductId(productId: number): Promise<ClassificationResponseDto[]> {
    try {
      this.logger.log(`Lấy danh sách phân loại cho sản phẩm ${productId}`);

      // Tìm tất cả phân loại của sản phẩm
      const classifications = await this.userClassificationRepository.findByProductId_user(productId);

      // Chuyển đổi sang DTO response
      const result: ClassificationResponseDto[] = [];
      for (const classification of classifications) {
        // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
        const customFieldsFromMetadata = classification.metadata?.customFields || [];
        const customFields: CustomFieldInputDto[] = customFieldsFromMetadata.map(cf => ({
          customFieldId: cf.customFieldId,
          value: cf.value,
        }));

        // Đảm bảo price có đầy đủ thông tin
        let price = classification.price;
        if (price && typeof price === 'object') {
          // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
          if (price.listPrice === undefined && price.value !== undefined) {
            price.listPrice = price.value;
          }
          if (price.salePrice === undefined && price.value !== undefined) {
            price.salePrice = price.value;
          }
          if (price.value === undefined && price.salePrice !== undefined) {
            price.value = price.salePrice;
          }
        }

        // Lấy ảnh thực tế từ metadata (không có imagesMediaTypes nữa)
        const imagesFromMetadata = classification.metadata?.images || [];
        const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

        // Thêm vào kết quả
        result.push({
          id: classification.id,
          type: classification.type,
          price,
          customFields,
          imagesMediaTypes: [], // Không lưu trong metadata nữa
          images: imagesWithUrls, // Ảnh thực tế với URL CDN
        });
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
        `Lỗi khi lấy danh sách phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết phân loại theo ID
   * @param id ID của phân loại
   * @returns Chi tiết phân loại
   */
  async getById(id: number): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết phân loại với ID ${id}`);

      // Tìm phân loại theo ID
      const classification = await this.userClassificationRepository.findById(id);
      if (!classification) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND,
          `Không tìm thấy phân loại với ID ${id}`,
        );
      }

      // Lấy custom fields từ metadata (classification custom fields sử dụng customFieldId)
      const customFieldsFromMetadata = classification.metadata?.customFields || [];
      const customFields: CustomFieldInputDto[] = customFieldsFromMetadata.map(cf => ({
        customFieldId: cf.customFieldId,
        value: cf.value,
      }));

      // Đảm bảo price có đầy đủ thông tin
      let price = classification.price;
      if (price && typeof price === 'object') {
        // Đảm bảo có đầy đủ các trường listPrice, salePrice, value, currency
        if (price.listPrice === undefined && price.value !== undefined) {
          price.listPrice = price.value;
        }
        if (price.salePrice === undefined && price.value !== undefined) {
          price.salePrice = price.value;
        }
        if (price.value === undefined && price.salePrice !== undefined) {
          price.value = price.salePrice;
        }
      }

      // Lấy ảnh thực tế từ metadata (không có imagesMediaTypes nữa)
      const imagesFromMetadata = classification.metadata?.images || [];
      const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: classification.id,
        type: classification.type,
        price,
        customFields,
        imagesMediaTypes: [], // Không lưu trong metadata nữa
        images: imagesWithUrls, // Ảnh thực tế với URL CDN
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
        `Lỗi khi lấy chi tiết phân loại: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra giá sản phẩm theo loại giá
   * @param price Giá của phân loại
   * @param typePrice Loại giá của sản phẩm
   * @param classificationType Loại phân loại
   * @throws AppException nếu giá không hợp lệ
   */
  private validateClassificationPrice(price: any, typePrice: PriceTypeEnum, classificationType: string): void {
    switch (typePrice) {
      case PriceTypeEnum.HAS_PRICE:
        // Kiểm tra có đủ các trường cần thiết không
        if (!price || !price.listPrice || !price.salePrice || !price.currency) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có đầy đủ listPrice, salePrice và currency khi sản phẩm có loại giá HAS_PRICE`,
          );
        }

        // Kiểm tra giá bán phải nhỏ hơn hoặc bằng giá niêm yết
        if (price.salePrice > price.listPrice) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Giá bán (salePrice) phải nhỏ hơn hoặc bằng giá niêm yết (listPrice) trong phân loại "${classificationType}"`,
          );
        }
        break;

      case PriceTypeEnum.STRING_PRICE:
        // Kiểm tra có trường priceDescription không
        if (!price || !price.priceDescription) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có priceDescription khi sản phẩm có loại giá STRING_PRICE`,
          );
        }
        break;

      case PriceTypeEnum.NO_PRICE:
        // Kiểm tra price phải là null
        if (price !== null) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" không được có giá khi sản phẩm có loại giá NO_PRICE`,
          );
        }
        break;

      // Các loại giá khác như HOURLY, DAILY, MONTHLY, YEARLY, CONTACT
      default:
        // Kiểm tra có đủ các trường cần thiết không
        if (!price || !price.value || !price.currency) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CLASSIFICATION_VALIDATION_FAILED,
            `Phân loại "${classificationType}" phải có đầy đủ value và currency khi sản phẩm có loại giá ${typePrice}`,
          );
        }
        break;
    }
  }

  /**
   * Tạo presigned URLs cho việc tải lên ảnh classification
   * @param classificationId ID của classification
   * @param imagesMediaTypes Danh sách MIME types của ảnh
   * @returns Danh sách upload URLs
   */
  private async createClassificationImageUploadUrls(
    classificationId: number,
    imagesMediaTypes: string[]
  ): Promise<any[]> {
    const uploadUrls: any[] = [];
    const now = Date.now();

    for (let i = 0; i < imagesMediaTypes.length; i++) {
      try {
        const mediaType = imagesMediaTypes[i] as ImageTypeEnum;

        // Tạo tên file với classification ID và timestamp
        const fileName = `classification-${classificationId}-image-${i}-${now}`;

        // Tạo S3 key cho ảnh classification
        const key = generateS3Key({
          baseFolder: 'business',
          categoryFolder: CategoryFolderEnum.IMAGE,
          fileName: fileName,
          useTimeFolder: true,
        });

        // Tạo presigned URL
        const url = await this.s3Service.createPresignedWithID(
          key,
          TimeIntervalEnum.FIFTEEN_MINUTES,
          mediaType,
          FileSizeEnum.FIVE_MB,
        );

        this.logger.debug(`Created presigned URL for classification image upload: ${key} with position ${i}`);

        uploadUrls.push({
          url: url,
          key: key,
          index: i
        });
      } catch (error) {
        this.logger.error(`Lỗi khi tạo upload URL cho ảnh classification: ${error.message}`, error.stack);
        // Tiếp tục với ảnh tiếp theo thay vì dừng toàn bộ
      }
    }

    return uploadUrls;
  }

  /**
   * Tạo URL ảnh với CDN cho danh sách ảnh
   * @param images Danh sách ảnh từ metadata
   * @returns Danh sách ảnh với URL CDN
   */
  private async generateImageUrls(images: Array<{key: string, size?: number, position?: number}>): Promise<Array<{key: string, size?: number, position?: number, url?: string}>> {
    if (!images || images.length === 0) {
      return [];
    }

    return Promise.all(
      images.map(async (image) => {
        try {
          // Tạo CDN signed URL
          const url = this.cdnService.generateUrlView(image.key, TimeIntervalEnum.ONE_HOUR);
          const timestamp = Date.now();

          return {
            key: image.key,
            size: image.size,
            position: image.position || 0,
            url: url ? `${url}?t=${timestamp}` : ''
          };
        } catch (error) {
          this.logger.error(`Lỗi khi tạo URL cho ảnh classification: ${error.message}`, error.stack);
          return {
            key: image.key,
            size: image.size,
            position: image.position || 0,
            url: ''
          };
        }
      })
    );
  }

  /**
   * Cập nhật thông tin ảnh thực tế cho classification sau khi upload
   * @param classificationId ID của classification
   * @param userId ID của user
   * @param images Danh sách ảnh đã upload
   * @returns Classification đã cập nhật
   */
  @Transactional()
  async updateClassificationImages(
    classificationId: number,
    userId: number,
    images: Array<{key: string, size: number, position: number}>
  ): Promise<ClassificationResponseDto> {
    try {
      this.logger.log(`Cập nhật ảnh cho classification ${classificationId}, userId=${userId}`);

      // Kiểm tra classification có tồn tại và thuộc về user không
      const classification = await this.userClassificationRepository
        .createQueryBuilder('classification')
        .leftJoinAndSelect('classification.product', 'product')
        .where('classification.id = :classificationId', { classificationId })
        .andWhere('product.createdBy = :userId', { userId })
        .getOne();

      if (!classification) {
        throw new AppException(BUSINESS_ERROR_CODES.CLASSIFICATION_NOT_FOUND);
      }

      // Lấy metadata hiện tại
      const currentMetadata = classification.metadata || { customFields: [] };

      // Cập nhật metadata với thông tin ảnh thực tế (không lưu imagesMediaTypes)
      const newMetadata = this.metadataHelper.buildClassificationMetadata(
        currentMetadata.customFields || [],
        [], // Không lưu imagesMediaTypes vào metadata
        images // Thông tin ảnh thực tế
      );

      // Cập nhật classification
      classification.metadata = newMetadata;
      const updatedClassification = await this.userClassificationRepository.save(classification);

      this.logger.log(`Đã cập nhật ảnh cho classification ${classificationId}`);

      // Xử lý price
      let price = updatedClassification.price;
      if (typeof price === 'string') {
        price = JSON.parse(price);
      }
      if (typeof price === 'object' && price !== null) {
        if (price.salePrice !== undefined && price.salePrice !== null) {
          price.value = price.salePrice;
        }
      }

      // Xử lý custom fields response
      const customFieldsResponse = currentMetadata.customFields || [];

      // Lấy ảnh thực tế từ metadata và tạo URL CDN
      const imagesFromMetadata = updatedClassification.metadata?.images || [];
      const imagesWithUrls = await this.generateImageUrls(imagesFromMetadata);

      // Trả về kết quả
      return {
        id: updatedClassification.id,
        type: updatedClassification.type,
        price,
        customFields: customFieldsResponse,
        imagesMediaTypes: [], // Không lưu trong metadata nữa
        images: imagesWithUrls, // Ảnh thực tế với URL CDN
      };

    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật ảnh classification: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BUSINESS_ERROR_CODES.CLASSIFICATION_UPDATE_FAILED);
    }
  }
}
