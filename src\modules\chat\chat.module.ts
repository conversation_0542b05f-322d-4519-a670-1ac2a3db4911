import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChatService } from './services/chat.service';
import { ChatController } from './controllers/chat.controller';
import {
  Agent,
  AgentSystem,
  AgentUser,
  UserMultiAgent
} from '@modules/agent/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Agent,
      AgentSystem,
      AgentUser,
      UserMultiAgent,
    ]),
  ],
  controllers: [
    ChatController,
  ],
  providers: [
    ChatService,
  ],
  exports: [
    ChatService,
  ],
})
export class ChatModule {}
