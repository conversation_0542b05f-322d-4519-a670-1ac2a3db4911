import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import {
  Controller,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags
} from '@nestjs/swagger';
import { AdminModelRegistryService } from '../services';

/**
 * Controller xử lý API cho Admin Model Registry
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_MODEL_REGISTRY)
@Controller('admin/model-registry')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminModelRegistryController {
  constructor(private readonly adminModelRegistryService: AdminModelRegistryService) { }
}
