import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '../../exceptions';
import { GHTK_TEST_CONFIG } from '@modules/business/constants';

/**
 * Helper để validate cấu hình GHTK
 */
@Injectable()
export class GHTKConfigValidationHelper {
  private readonly logger = new Logger(GHTKConfigValidationHelper.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Validate cấu hình GHTK từ environment variables
   */
  validateGHTKConfig(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    config: {
      token: string;
      partnerCode?: string;
      baseUrl: string;
      timeout: number;
      isTestMode: boolean;
    };
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Lấy cấu hình từ environment
    const token = this.configService.get<string>('GHTK_TOKEN');
    const partnerCode = this.configService.get<string>('GHTK_PARTNER_CODE');
    const baseUrl = this.configService.get<string>('GHTK_BASE_URL');
    const timeout = this.configService.get<number>('GHTK_TIMEOUT');
    const isTestMode = this.configService.get<boolean>('GHTK_TEST_MODE');

    // Validate token
    if (!token) {
      warnings.push('GHTK_TOKEN không được cấu hình, sử dụng token test mặc định');
    } else if (token === GHTK_TEST_CONFIG.TOKEN) {
      warnings.push('Đang sử dụng GHTK token test, không phù hợp cho production');
    } else if (!this.isValidTokenFormat(token)) {
      errors.push('GHTK_TOKEN có format không hợp lệ');
    }

    // Validate partner code (optional)
    if (partnerCode && partnerCode === GHTK_TEST_CONFIG.PARTNER_CODE) {
      warnings.push('Đang sử dụng GHTK partner code test, không phù hợp cho production');
    }

    // Validate base URL
    if (!baseUrl) {
      warnings.push('GHTK_BASE_URL không được cấu hình, sử dụng URL mặc định');
    } else if (!this.isValidUrl(baseUrl)) {
      errors.push('GHTK_BASE_URL có format không hợp lệ');
    }

    // Validate timeout
    if (timeout && (timeout < 1000 || timeout > 120000)) {
      warnings.push('GHTK_TIMEOUT nên trong khoảng 1000-120000ms');
    }

    // Validate test mode
    if (isTestMode === false && token === GHTK_TEST_CONFIG.TOKEN) {
      errors.push('Không thể sử dụng token test khi GHTK_TEST_MODE=false');
    }

    // Production warnings
    if (!isTestMode) {
      if (token === GHTK_TEST_CONFIG.TOKEN) {
        errors.push('Production mode không thể sử dụng token test');
      }
      if (partnerCode && partnerCode === GHTK_TEST_CONFIG.PARTNER_CODE) {
        warnings.push('Production mode đang sử dụng partner code test');
      }
    }

    const config = {
      token: token || GHTK_TEST_CONFIG.TOKEN,
      partnerCode: partnerCode || undefined,
      baseUrl: baseUrl || GHTK_TEST_CONFIG.BASE_URL,
      timeout: timeout || 30000,
      isTestMode: isTestMode ?? true
    };

    const isValid = errors.length === 0;

    // Log validation results
    if (errors.length > 0) {
      this.logger.error('GHTK configuration validation failed:', errors);
    }
    if (warnings.length > 0) {
      this.logger.warn('GHTK configuration warnings:', warnings);
    }
    if (isValid) {
      this.logger.log('GHTK configuration validation passed', {
        hasCustomToken: token !== GHTK_TEST_CONFIG.TOKEN,
        hasCustomPartnerCode: partnerCode !== GHTK_TEST_CONFIG.PARTNER_CODE,
        isTestMode: config.isTestMode
      });
    }

    return {
      isValid,
      errors,
      warnings,
      config
    };
  }

  /**
   * Validate và throw exception nếu cấu hình không hợp lệ
   */
  validateAndThrow(): void {
    const validation = this.validateGHTKConfig();
    
    if (!validation.isValid) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_CONFIG_ERROR,
        `Cấu hình GHTK không hợp lệ: ${validation.errors.join(', ')}`
      );
    }
  }

  /**
   * Kiểm tra format token GHTK
   */
  private isValidTokenFormat(token: string): boolean {
    // GHTK token thường có format: APITokenSample-xxxxxxxxxxxxxxxxxxxxxxxx
    // hoặc các format khác tùy theo GHTK
    if (!token || token.length < 10) {
      return false;
    }

    // Kiểm tra các ký tự không hợp lệ
    const invalidChars = /[<>"'&\s]/;
    if (invalidChars.test(token)) {
      return false;
    }

    return true;
  }

  /**
   * Kiểm tra format URL
   */
  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * Lấy thông tin môi trường hiện tại
   */
  getEnvironmentInfo(): {
    nodeEnv: string;
    isProduction: boolean;
    isDevelopment: boolean;
    isTest: boolean;
  } {
    const nodeEnv = this.configService.get<string>('NODE_ENV') || 'development';
    
    return {
      nodeEnv,
      isProduction: nodeEnv === 'production',
      isDevelopment: nodeEnv === 'development',
      isTest: nodeEnv === 'test'
    };
  }

  /**
   * Kiểm tra cấu hình có phù hợp với môi trường không
   */
  validateEnvironmentCompatibility(): {
    isCompatible: boolean;
    recommendations: string[];
  } {
    const envInfo = this.getEnvironmentInfo();
    const validation = this.validateGHTKConfig();
    const recommendations: string[] = [];

    // Production environment checks
    if (envInfo.isProduction) {
      if (validation.config.isTestMode) {
        recommendations.push('Production environment nên set GHTK_TEST_MODE=false');
      }
      if (validation.config.token === GHTK_TEST_CONFIG.TOKEN) {
        recommendations.push('Production environment cần token GHTK thực tế');
      }
      if (validation.config.partnerCode && validation.config.partnerCode === GHTK_TEST_CONFIG.PARTNER_CODE) {
        recommendations.push('Production environment cần partner code GHTK thực tế');
      }
    }

    // Development environment checks
    if (envInfo.isDevelopment) {
      if (!validation.config.isTestMode) {
        recommendations.push('Development environment nên set GHTK_TEST_MODE=true');
      }
    }

    // Test environment checks
    if (envInfo.isTest) {
      if (!validation.config.isTestMode) {
        recommendations.push('Test environment nên set GHTK_TEST_MODE=true');
      }
    }

    return {
      isCompatible: !recommendations.length,
      recommendations
    };
  }

  /**
   * Tạo báo cáo cấu hình GHTK
   */
  generateConfigReport(): string {
    const validation = this.validateGHTKConfig();
    const envInfo = this.getEnvironmentInfo();
    const compatibility = this.validateEnvironmentCompatibility();

    let report = '=== GHTK Configuration Report ===\n\n';
    
    // Environment info
    report += `Environment: ${envInfo.nodeEnv}\n`;
    report += `Test Mode: ${validation.config.isTestMode}\n`;
    report += `Base URL: ${validation.config.baseUrl}\n`;
    report += `Timeout: ${validation.config.timeout}ms\n`;
    report += `Has Custom Token: ${validation.config.token !== GHTK_TEST_CONFIG.TOKEN}\n`;
    report += `Has Custom Partner Code: ${validation.config.partnerCode && validation.config.partnerCode !== GHTK_TEST_CONFIG.PARTNER_CODE}\n\n`;

    // Validation status
    report += `Validation Status: ${validation.isValid ? 'PASSED' : 'FAILED'}\n`;
    
    if (validation.errors.length > 0) {
      report += `Errors:\n${validation.errors.map(e => `  - ${e}`).join('\n')}\n`;
    }
    
    if (validation.warnings.length > 0) {
      report += `Warnings:\n${validation.warnings.map(w => `  - ${w}`).join('\n')}\n`;
    }

    // Environment compatibility
    report += `\nEnvironment Compatibility: ${compatibility.isCompatible ? 'COMPATIBLE' : 'NEEDS ATTENTION'}\n`;
    
    if (compatibility.recommendations.length > 0) {
      report += `Recommendations:\n${compatibility.recommendations.map(r => `  - ${r}`).join('\n')}\n`;
    }

    return report;
  }
}
