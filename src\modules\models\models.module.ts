import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ModelsAdminModule } from './admin/models-admin.module';
import { ModelsUserModule } from './user/models-user.module';
import * as entities from './entities';

/**
 * Module chính cho Model Management System
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.ModelRegistry,
      entities.SystemKeyLlm,
      entities.FineTuneHistories,
      entities.UserKeyLlm,
      entities.UserDataFineTune,
      entities.AdminDataFineTune,
      // New entities
      entities.UserModels,
      entities.UserModelKeyLlm,
      entities.UserModelFineTune,
      entities.SystemModels,
      entities.SystemModelKeyLlm,
    ]),
    ModelsAdminModule,
    ModelsUserModule,
  ],
  providers: [],
  exports: [
    ModelsAdminModule,
    ModelsUserModule,
    TypeOrmModule,
  ],
})
export class ModelsModule {}
