import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsObject,
  ValidateNested
} from 'class-validator';

/**
 * DTO cho quy trình xử lý đơn hàng số (Digital Fulfillment Flow)
 */
export class DigitalFulfillmentFlowDto {
  @ApiProperty({
    description: 'Cách giao hàng số',
    example: 'Gửi qua email',
    enum: ['email', 'dashboard_download', 'course_activation', 'license_key'],
  })
  @IsString()
  @IsNotEmpty()
  deliveryMethod: string;

  @ApiProperty({
    description: 'Thời điểm giao hàng',
    example: 'Sau khi thanh toán thành công',
    enum: ['immediate', 'after_payment', 'scheduled'],
  })
  @IsString()
  @IsNotEmpty()
  deliveryTiming: string;

  @ApiProperty({
    description: 'Thời gian chờ giao hàng (phút)',
    example: 0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  deliveryDelayMinutes?: number;

  @ApiProperty({
    description: 'Tình trạng truy cập',
    example: 'Đã giao',
    enum: ['delivered', 'pending', 'failed'],
  })
  @IsString()
  @IsNotEmpty()
  accessStatus: string;
}

/**
 * DTO cho đầu ra sản phẩm số (Digital Output/Access)
 */
export class DigitalOutputDto {
  @ApiProperty({
    description: 'Loại sản phẩm số',
    example: 'Khóa học online',
    enum: ['online_course', 'downloadable_file', 'license_key', 'ebook'],
  })
  @IsString()
  @IsNotEmpty()
  outputType: string;

  @ApiProperty({
    description: 'Đường link kích hoạt hoặc truy cập',
    example: 'https://course.example.com/activate?token=abc123',
    required: false,
  })
  @IsOptional()
  @IsString()
  accessLink?: string;

  @ApiProperty({
    description: 'Thông tin đăng nhập (JSON)',
    example: { username: 'user123', password: 'temp_password' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  loginInfo?: any;

  @ApiProperty({
    description: 'Link tải file (có thời hạn hoặc không)',
    example: 'https://cdn.example.com/download/file123.pdf?expires=**********',
    required: false,
  })
  @IsOptional()
  @IsString()
  downloadLink?: string;

  @ApiProperty({
    description: 'Key bản quyền',
    example: 'ABCD-EFGH-IJKL-MNOP',
    required: false,
  })
  @IsOptional()
  @IsString()
  licenseKey?: string;

  @ApiProperty({
    description: 'Hướng dẫn sử dụng',
    example: 'Vui lòng nhập key bản quyền vào phần mềm để kích hoạt',
    required: false,
  })
  @IsOptional()
  @IsString()
  usageInstructions?: string;
}

/**
 * DTO cho thông tin nâng cao của sản phẩm số
 */
export class DigitalProductDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 150,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  purchaseCount: number;

  @ApiProperty({
    description: 'Quy trình xử lý đơn hàng số',
    type: DigitalFulfillmentFlowDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalFulfillmentFlowDto)
  digitalFulfillmentFlow: DigitalFulfillmentFlowDto;

  @ApiProperty({
    description: 'Đầu ra sản phẩm số',
    type: DigitalOutputDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalOutputDto)
  digitalOutput: DigitalOutputDto;
}
